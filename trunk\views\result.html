{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> AI - AI Generator{% endblock %}

{% block css %}
<style>
        /* Vibrant Result Page */
        .apple-result-hero {
            background: var(--bg-hero);
            padding: var(--spacing-3xl) 0 var(--spacing-2xl);
            text-align: center;
            margin-top: 60px; /* Account for fixed nav */
            position: relative;
            overflow: hidden;
        }

        .apple-result-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.15) 0%, transparent 50%);
            pointer-events: none;
        }

        .apple-result-title {
            font-size: clamp(36px, 5vw, 64px);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-white);
            margin-bottom: var(--spacing-lg);
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .apple-result-subtitle {
            font-size: 22px;
            color: rgba(255,255,255,0.9);
            margin-bottom: var(--spacing-xl);
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
            position: relative;
            z-index: 1;
            font-weight: var(--font-weight-medium);
        }

        .apple-result-layout {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-3xl);
            margin: var(--spacing-2xl) 0;
        }

        .result-top-section {
            width: 100%;
        }

        .result-bottom-section {
            width: 100%;
        }

        .apple-info-card-expanded {
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25);
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(20px);
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .apple-info-card {
            background: var(--bg-card);
            border-radius: var(--radius-2xl);
            box-shadow: 0 15px 50px rgba(102, 126, 234, 0.25);
            overflow: hidden;
            height: fit-content;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(20px);
        }

        .apple-info-header {
            padding: var(--spacing-lg);
            background: var(--primary-gradient);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .apple-info-title {
            font-size: 20px;
            font-weight: var(--font-weight-bold);
            color: var(--color-text-white);
            margin-bottom: var(--spacing-xs);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .apple-info-body-expanded {
            padding: var(--spacing-xl);
        }

        .apple-info-grid-expanded {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .apple-info-item-expanded {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) var(--spacing-lg);
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.04) 100%);
            border-radius: var(--radius-md);
            border: 1px solid rgba(255,255,255,0.15);
            transition: var(--transition);
            min-height: 50px;
        }

        .apple-info-item-expanded:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
            border-color: rgba(255,255,255,0.3);
        }

        .apple-info-item-expanded .apple-info-label {
            font-size: 15px;
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-secondary);
            min-width: 120px;
        }

        .apple-info-item-expanded .apple-info-value {
            font-size: 15px;
            font-weight: var(--font-weight-medium);
            color: var(--color-text);
            text-align: right;
            flex: 1;
            word-break: break-word;
        }

        .apple-info-body {
            padding: var(--spacing-2xl);
        }

        .apple-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--color-surface);
            transition: var(--transition);
        }

        .apple-info-item:last-child {
            border-bottom: none;
        }

        .apple-info-label {
            font-weight: var(--font-weight-medium);
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
        }

        .apple-info-value {
            font-weight: var(--font-weight-medium);
            color: var(--color-text);
            text-align: right;
        }

        .apple-gallery-card-expanded {
            background: var(--bg-card);
            border-radius: var(--radius-2xl);
            box-shadow: 0 20px 60px rgba(240, 147, 251, 0.3);
            overflow: hidden;
            border: 3px solid rgba(255,255,255,0.4);
            backdrop-filter: blur(30px);
            width: 100%;
        }

        .apple-gallery-body-expanded {
            padding: var(--spacing-3xl);
        }

        .apple-images-grid-expanded {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-2xl);
        }

        .apple-image-item-expanded {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: var(--radius-xl);
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.2);
            transition: var(--transition);
            box-shadow: 0 12px 32px rgba(0,0,0,0.1);
        }

        .apple-image-item-expanded:hover {
            transform: translateY(-6px);
            box-shadow: 0 20px 50px rgba(240, 147, 251, 0.3);
            border-color: rgba(255,255,255,0.4);
        }

        .apple-image-display-expanded {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
            min-height: 400px;
        }

        .apple-image-display-expanded img {
            width: 100%;
            height: 100%;
            object-fit: contain; /* Hiển thị đầy đủ ảnh thay vì crop */
            transition: var(--transition);
            background: #f8f9fa; /* Background nhẹ để thấy rõ ảnh */
        }

        .apple-image-display-expanded:hover img {
            transform: scale(1.05);
        }

        .apple-download-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--success-gradient);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: var(--font-weight-semibold);
            transition: var(--transition);
            box-shadow: 0 4px 16px rgba(67, 233, 123, 0.3);
        }

        .apple-download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(67, 233, 123, 0.4);
            text-decoration: none;
            color: white;
        }

        .apple-gallery-card {
            background: var(--bg-card);
            border-radius: var(--radius-2xl);
            box-shadow: 0 15px 50px rgba(240, 147, 251, 0.25);
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(20px);
        }

        .apple-gallery-header {
            padding: var(--spacing-xl);
            background: var(--secondary-gradient);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .apple-gallery-title {
            font-size: 28px;
            font-weight: var(--font-weight-bold);
            color: var(--color-text-white);
            margin-bottom: var(--spacing-xs);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .apple-gallery-body {
            padding: var(--spacing-lg);
        }

        .apple-images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .apple-image-item {
            background: var(--color-background);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .apple-image-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .apple-image-display {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
        }

        .apple-image-display img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .apple-image-overlay {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-md);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            backdrop-filter: blur(10px);
        }

        .apple-image-actions {
            padding: var(--spacing-md);
            text-align: center;
        }

        .apple-action-section {
            text-align: center;
            margin: var(--spacing-3xl) 0;
        }

        .apple-action-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            flex-wrap: wrap;
        }

        @media (max-width: 1200px) {
            .apple-info-card-expanded {
                max-width: 700px;
            }

            .apple-info-grid-expanded {
                gap: var(--spacing-sm);
            }

            .apple-images-grid-expanded {
                grid-template-columns: 1fr;
                gap: var(--spacing-xl);
            }

            .apple-image-display-expanded {
                min-height: 350px;
            }
        }

        @media (max-width: 768px) {
            .apple-result-layout {
                gap: var(--spacing-2xl);
                margin: var(--spacing-lg) 0;
            }

            .apple-info-card-expanded {
                max-width: 100%;
                margin: 0 var(--spacing-md);
            }

            .apple-info-header {
                padding: var(--spacing-md);
            }

            .apple-info-title {
                font-size: 18px;
            }

            .apple-info-body-expanded {
                padding: var(--spacing-lg);
            }

            .apple-gallery-body-expanded {
                padding: var(--spacing-2xl);
            }

            .apple-info-grid-expanded {
                gap: var(--spacing-xs);
            }

            .apple-info-item-expanded {
                padding: var(--spacing-sm) var(--spacing-md);
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-xs);
                min-height: auto;
            }

            .apple-info-item-expanded .apple-info-label {
                font-size: 14px;
                min-width: auto;
            }

            .apple-info-item-expanded .apple-info-value {
                font-size: 14px;
                text-align: center;
            }

            .apple-images-grid-expanded {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }

            .apple-image-display-expanded {
                min-height: 300px;
            }

            .apple-action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .apple-button {
                width: 100%;
                max-width: 300px;
            }
        }
</style>
{% endblock %}

{% block content %}
<!-- Vibrant Result Hero -->
<section class="apple-result-hero">
    <div class="apple-container">
        <h1 class="apple-result-title">🎉 Tuyệt vời! Hoàn thành rồi! 🎉</h1>
        <p class="apple-result-subtitle">
            ✨ Ảnh AI của bạn đã được tạo thành công với chất lượng siêu đẹp! ✨<br>
            🚀 Hãy tải về và chia sẻ những tác phẩm tuyệt vời này! 🚀
        </p>
    </div>
</section>

<!-- Main Result Content -->
<main class="apple-container" style="max-width: 1600px;">
    <div class="apple-result-layout">
        <!-- Card Information Section - TOP -->
        <div class="result-top-section">
            <div class="apple-info-card-expanded">
                <div class="apple-info-header">
                    <h3 class="apple-info-title">📄 Thông tin đã trích xuất từ Business Card</h3>
                    <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 16px;">Dữ liệu được trích xuất bằng AI OCR</p>
                </div>
                <div class="apple-info-body-expanded">
                    {% if card_info %}
                    <div class="apple-info-grid-expanded">
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">👤 Tên</span>
                            <span class="apple-info-value">{{ card_info.name or 'N/A' }}</span>
                        </div>
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">💼 Chức vụ</span>
                            <span class="apple-info-value">{{ card_info.title or card_info.occupation or 'N/A' }}</span>
                        </div>
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">🏢 Công ty</span>
                            <span class="apple-info-value">{{ card_info.company or 'N/A' }}</span>
                        </div>
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">📧 Email</span>
                            <span class="apple-info-value">{{ card_info.email or 'N/A' }}</span>
                        </div>
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">📱 Điện thoại</span>
                            <span class="apple-info-value">{{ card_info.phone or 'N/A' }}</span>
                        </div>
                        <div class="apple-info-item-expanded">
                            <span class="apple-info-label">🌐 Website</span>
                            <span class="apple-info-value">{{ card_info.website or 'N/A' }}</span>
                        </div>
                    </div>
                    {% else %}
                    <div style="text-align: center; padding: var(--spacing-2xl); color: var(--color-text-secondary);">
                        <p style="font-size: 18px;">Không có thông tin business card được trích xuất</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Generated Images Gallery - BOTTOM -->
        <div class="result-bottom-section">
            <div class="apple-gallery-card-expanded">
                <div class="apple-gallery-header">
                    <h3 class="apple-gallery-title">🎨 Kết quả AI Generated Images</h3>
                    <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 16px;">Ảnh được tạo bằng Gemini 2.0 Flash Preview</p>
                </div>
                <div class="apple-gallery-body-expanded">
                    {% if generated_images %}
                    <div class="apple-images-grid-expanded">
                        {% for image_info in generated_images %}
                        <div class="apple-image-item-expanded">
                            <div class="apple-image-display-expanded">
                                <img src="{{ '/' + image_info.path }}" alt="AI Generated Image {{ loop.index }}" onerror="this.src='/static/placeholder.svg'">
                                <div class="apple-image-overlay">{{ image_info.title or 'Phiên bản ' + loop.index|string }}</div>
                            </div>
                            <div class="apple-image-actions">
                                <a href="{{ '/' + image_info.path }}" download="{{ (card_info.name or 'AI_Image') | replace(' ', '_') }}_{{ loop.index }}.jpg" class="apple-download-btn">
                                    📥 Tải về ảnh {{ loop.index }}
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div style="text-align: center; padding: var(--spacing-3xl); color: var(--color-text-secondary);">
                        <p style="font-size: 18px;">Chưa có ảnh AI nào được tạo</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</main>

    <!-- Action Buttons -->
    <section class="apple-action-section">
        <h3 style="margin-bottom: var(--spacing-lg); color: var(--color-text-white); text-shadow: 0 2px 4px rgba(0,0,0,0.3); font-size: 24px;">🚀 Tiếp theo làm gì? 🚀</h3>
        <div class="apple-action-buttons">
            <a href="/" class="apple-button apple-button-primary" style="background: var(--warning-gradient); font-size: 18px; padding: 18px 36px;">
                <span style="font-size: 20px;">🔄</span>
                Tạo ảnh mới siêu đẹp
            </a>
            <button class="apple-button apple-button-secondary" onclick="resetSession()" style="background: var(--danger-gradient); font-size: 16px;">
                <span style="font-size: 18px;">🗑️</span>
                Xóa session
            </button>
        </div>
    </section>
</main>
{% endblock %}

{% block js %}
<script>
function downloadImage(imagePath, filename) {
    const link = document.createElement('a');
    link.href = '/' + imagePath;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function resetSession() {
    if (confirm('Bạn có chắc muốn xóa session hiện tại?')) {
        fetch('/reset_session', {
            method: 'POST'
        })
        .then(res => res.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.href = '/';
            }
        })
        .catch(error => {
            alert('Lỗi reset session: ' + error);
        });
    }
}
</script>
{% endblock %}


