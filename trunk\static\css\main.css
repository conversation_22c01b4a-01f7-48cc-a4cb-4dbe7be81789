@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --accent-bg: #16213e;
    --card-bg: rgba(26, 26, 46, 0.8);
    --glass-bg: rgba(255, 255, 255, 0.05);

    /* Accent Colors */
    --primary-blue: #4facfe;
    --secondary-blue: #00f2fe;
    --accent-purple: #8a2be2;
    --success-green: #00ff88;
    --warning-orange: #ff6b35;
    --error-red: #ff4757;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8a8aa0;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(79, 172, 254, 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background:
        radial-gradient(circle at 20% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background:
        linear-gradient(135deg, var(--card-bg) 0%, rgba(22, 33, 62, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(79, 172, 254, 0.2);
    padding: var(--spacing-xl) var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
    animation: headerShimmer 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes headerShimmer {
    0%, 100% { transform: translateX(-100%); opacity: 0; }
    50% { transform: translateX(100%); opacity: 1; }
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-lg);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-glow);
}

.logo-text h1 {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.header-subtitle {
    text-align: right;
}

.header-subtitle p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

.header-decoration {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
}

.subtitle {
    font-size: 1.3em;
    opacity: 0.8;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.02em;
    position: relative;
    z-index: 2;
}

.header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.main-content {
    flex: 1;
    padding: 20px 40px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    background:
        linear-gradient(135deg, #ffffff 0%, #f8f9fa 25%, #ffffff 50%, #f8f9fa 75%, #ffffff 100%),
        radial-gradient(circle at 30% 30%, rgba(79, 172, 254, 0.05) 0%, transparent 60%),
        radial-gradient(circle at 70% 70%, rgba(138, 43, 226, 0.03) 0%, transparent 60%);
    border-radius: 20px;
    margin: 10px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-height: calc(100vh - 200px);
    max-width: none;
}

.camera-section {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    margin-bottom: 0;
    height: calc(100vh - 150px);
    min-height: 700px;
    width: 100%;
}

.camera-container {
    flex: 1;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 32px;
    border: 1px solid rgba(79, 172, 254, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.camera-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.camera-container:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(79, 172, 254, 0.4);
    box-shadow:
        0 25px 50px rgba(79, 172, 254, 0.15),
        0 10px 25px rgba(0, 0, 0, 0.1);
}

.camera-container:hover::before {
    opacity: 1;
}

.camera-title {
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 24px;
    color: #ffffff;
    text-align: center;
    position: relative;
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;
}

.camera-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 2px;
}

.camera-title.card {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.camera-title.card::after {
    background: linear-gradient(90deg, #ff6b6b, #ee5a24);
}

.camera-title.face {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.camera-title.face::after {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.camera-frame {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 24px;
    background:
        linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(79, 172, 254, 0.3);
    transition: all 0.3s ease;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-frame:hover {
    transform: scale(1.01);
    box-shadow:
        0 20px 40px rgba(79, 172, 254, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.camera-frame img {
    width: 100%;
    height: 100%;
    min-height: 500px;
    max-height: none;
    object-fit: contain;
    object-position: center;
    display: block;
    transition: transform 0.3s ease;
}

.camera-frame:hover img {
    transform: scale(1.05);
}

/* Scan Effects Styles - Chỉ Wave Scan */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

/* Subtle Wave Scan Effect - Single Color */
.wave-scan {
    background: linear-gradient(
        90deg,
        transparent 0%,
        transparent 30%,
        rgba(0, 255, 255, 0.05) 40%,
        rgba(0, 255, 255, 0.15) 50%,
        rgba(0, 255, 255, 0.05) 60%,
        transparent 70%,
        transparent 100%
    );
    background-size: 200% 100%;
    animation: waveScan 3s linear infinite;
    filter: blur(1px);
}

@keyframes waveScan {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Subtle Glow Effect */
.scan-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 50% 50%,
        rgba(0, 255, 255, 0.08) 0%,
        transparent 60%
    );
    animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* Modern Detection Status Overlay */
.detection-status {
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
    background: rgba(15, 15, 35, 0.5);
    color: #ffffff;
    padding: 16px 20px;
    border-radius: 16px;
    font-size: 0.95em;
    font-weight: 600;
    text-align: center;
    z-index: 20;
    transition: all 0.4s ease;
    border: 1px solid rgba(79, 172, 254, 0.15);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    opacity: 0.5;
}

.detection-status.valid {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.5) 0%, rgba(39, 174, 96, 0.5) 100%);
    border: 1px solid rgba(46, 204, 113, 0.25);
    color: white;
    box-shadow:
        0 8px 25px rgba(46, 204, 113, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    opacity: 0.5;
}

.detection-status.invalid {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.5) 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: white;
    box-shadow:
        0 8px 25px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    opacity: 0.5;
}

.detection-status.scanning {
    background: linear-gradient(135deg, rgba(241, 196, 15, 0.5) 0%, rgba(243, 156, 18, 0.5) 100%);
    border: 1px solid rgba(241, 196, 15, 0.25);
    color: #ffffff;
    animation: pulse 1.5s infinite;
    box-shadow:
        0 8px 25px rgba(241, 196, 15, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    opacity: 0.5;
}

/* Modern Corner Brackets */
.corner-brackets {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 15;
}

.corner-brackets::before,
.corner-brackets::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid #667eea;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.corner-brackets::before {
    top: 10px;
    left: 10px;
    border-right: none;
    border-bottom: none;
}

.corner-brackets::after {
    bottom: 10px;
    right: 10px;
    border-left: none;
    border-top: none;
}

/* Additional corner brackets */
.corner-brackets-extra::before,
.corner-brackets-extra::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid #764ba2;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.corner-brackets-extra::before {
    top: 10px;
    right: 10px;
    border-left: none;
    border-bottom: none;
}

.corner-brackets-extra::after {
    bottom: 10px;
    left: 10px;
    border-right: none;
    border-top: none;
}

.camera-frame:hover .corner-brackets::before,
.camera-frame:hover .corner-brackets::after,
.camera-frame:hover .corner-brackets-extra::before,
.camera-frame:hover .corner-brackets-extra::after {
    opacity: 1;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2em;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(5px);
}

.camera-frame:hover .camera-overlay {
    opacity: 1;
}

.button-group {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn {
    padding: 16px 32px;
    border: none;
    border-radius: 16px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    min-width: 160px;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.02em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    box-shadow:
        0 10px 30px rgba(79, 172, 254, 0.3),
        0 4px 15px rgba(0, 242, 254, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 15px 40px rgba(79, 172, 254, 0.4),
        0 6px 20px rgba(0, 242, 254, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 25px rgba(255, 107, 107, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    transform: translateY(-2px) scale(1.02);
    background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
    box-shadow:
        0 12px 30px rgba(255, 107, 107, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #495057;
    border: 1px solid #dee2e6;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.2),
        0 5px 15px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.generate-section {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    margin-top: 30px;
    border: 1px solid #dee2e6;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: none;
}

.generate-section h3 {
    color: #495057;
    margin-bottom: 15px;
}

.generate-section p {
    color: #6c757d;
}

.generate-btn {
    font-size: 1.3em;
    padding: 15px 40px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
}

.generate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: #6c757d;
}

.camera-status-line {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.status-indicator {
    display: inline-block;
    font-size: 1em;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: transparent;
    border: none;
}

.camera-info {
    font-size: 1.1em;
    font-weight: 500;
    text-align: center;
}

.status-ready { background: transparent; }
.status-captured { background: transparent; }
.status-processing { background: transparent; animation: pulse 1.5s infinite; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* AI Configuration Modal Styles */
.ai-config-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.config-card {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
    border: 3px solid #667eea;
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
    margin: auto;
}

.config-card h3 {
    margin: 0 0 30px 0;
    color: #333;
    font-size: 1.8em;
    text-align: center;
    font-weight: bold;
}

.config-group {
    margin-bottom: 20px;
}

.config-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #555;
}

.config-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1em;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.config-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.config-textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #ddd;
    border-radius: 12px;
    font-size: 1.1em;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    background: white;
    resize: vertical;
    min-height: 200px;
    line-height: 1.6;
    transition: border-color 0.3s ease;
}

.config-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-section {
        flex-direction: column;
        height: auto;
        min-height: auto;
        gap: 30px;
    }

    .camera-container {
        height: 400px;
        min-height: 400px;
    }

    .header {
        padding: 30px 40px;
    }

    .header h1 {
        font-size: 2.5em;
    }

    .main-content {
        padding: 30px 40px;
    }

    .camera-container {
        padding: 24px;
    }

    .camera-frame img {
        min-height: 450px;
        max-height: none;
        object-fit: contain;
    }

    .btn {
        padding: 14px 28px;
        font-size: 0.95em;
    }
}

@media (max-width: 480px) {
    .camera-section {
        gap: 20px;
    }

    .camera-container {
        height: 350px;
        min-height: 350px;
        padding: 20px;
    }

    .header {
        padding: 20px 30px;
    }

    .header h1 {
        font-size: 2em;
    }

    .main-content {
        padding: 20px 30px;
    }

    .camera-frame img {
        min-height: 400px;
        max-height: none;
        object-fit: contain;
    }
}
