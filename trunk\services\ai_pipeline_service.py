#!/usr/bin/env python3
"""
Two-Stage AI Pipeline Service for Business Card Processing and Image Generation

Stage 1: OCR Processing with Gemini 2.5 Flash
Stage 2: AI Image Generation with Gemini 2.0 Flash Preview
"""

import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Import OCR service (Stage 1)
from gemini_ocr_service import GeminiOCRService

# Import AI image generator (Stage 2)  
from ai_generator import AIImageGenerator

# Import configuration
from ai_config import get_gemini_config

class AIProcessingPipeline:
    """
    Two-stage AI pipeline for business card processing and image generation
    
    Stage 1: OCR Processing (Gemini 2.5 Flash)
    - Extract text from business card images with maximum accuracy
    - Apply image preprocessing for better OCR results
    - Use deterministic API settings for consistent results
    
    Stage 2: AI Image Generation (Gemini 2.0 Flash Preview)
    - Generate AI images using multimodal capabilities
    - Combine prompt template, OCR data, and face image
    - Create multiple variants with proper metadata
    """
    
    def __init__(self):
        """Initialize the two-stage AI pipeline"""
        print("🔧 Initializing Two-Stage AI Pipeline...")
        
        # Initialize Stage 1: OCR Service (Gemini 2.5 Flash)
        print("📝 Stage 1: Initializing OCR Service (Gemini 2.5 Flash)...")
        self.ocr_service = GeminiOCRService()
        
        # Initialize Stage 2: AI Image Generator (Gemini 2.0 Flash Preview)
        print("🎨 Stage 2: Initializing AI Image Generator (Gemini 2.0 Flash Preview)...")
        self.ai_generator = AIImageGenerator()
        
        # Pipeline configuration
        self.config = get_gemini_config()
        
        # Pipeline state
        self.current_session = None
        self.stage1_result = None
        self.stage2_result = None
        
        print("✅ Two-Stage AI Pipeline initialized successfully")
        print(f"   Stage 1: OCR with Gemini 2.5 Flash")
        print(f"   Stage 2: Image Generation with Gemini 2.0 Flash Preview")
    
    def process_business_card(self, card_image_path: str, face_image_path: str, 
                            prompt_template: str = 'cartoon', session_id: str = None) -> Dict:
        """
        Execute the complete two-stage AI pipeline
        
        Args:
            card_image_path: Path to business card image
            face_image_path: Path to face reference image
            prompt_template: Style template (cartoon/luxury/artistic)
            session_id: Optional session identifier
            
        Returns:
            Dict containing results from both stages
        """
        
        pipeline_start_time = time.time()
        
        print(f"\n🚀 Starting Two-Stage AI Pipeline")
        print(f"   Session ID: {session_id or 'auto-generated'}")
        print(f"   Card Image: {card_image_path}")
        print(f"   Face Image: {face_image_path}")
        print(f"   Style Template: {prompt_template}")
        print("=" * 60)
        
        # Initialize session
        if not session_id:
            session_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = {
            'session_id': session_id,
            'start_time': pipeline_start_time,
            'card_image_path': card_image_path,
            'face_image_path': face_image_path,
            'prompt_template': prompt_template,
            'stage1_result': None,
            'stage2_result': None,
            'status': 'processing'
        }
        
        try:
            # Stage 1: OCR Processing
            stage1_result = self._execute_stage1_ocr(card_image_path)
            if not stage1_result['success']:
                return self._create_error_result("Stage 1 OCR failed", stage1_result['error'])
            
            # Stage 2: AI Image Generation
            stage2_result = self._execute_stage2_generation(
                face_image_path, 
                stage1_result['data'], 
                prompt_template
            )
            if not stage2_result['success']:
                return self._create_error_result("Stage 2 Generation failed", stage2_result['error'])
            
            # Pipeline completed successfully
            pipeline_end_time = time.time()
            total_time = pipeline_end_time - pipeline_start_time
            
            result = {
                'success': True,
                'session_id': session_id,
                'total_processing_time': f"{total_time:.2f}s",
                'stage1_ocr': stage1_result,
                'stage2_generation': stage2_result,
                'pipeline_summary': {
                    'card_info_extracted': stage1_result['data'],
                    'images_generated': len(stage2_result['data']['generated_images']),
                    'processing_stages': ['OCR (Gemini 2.5)', 'Generation (Gemini 2.0)']
                }
            }
            
            self.current_session['status'] = 'completed'
            self.current_session['result'] = result
            
            print(f"\n✅ Two-Stage AI Pipeline Completed Successfully!")
            print(f"   Total Time: {total_time:.2f}s")
            print(f"   Stage 1 Time: {stage1_result['processing_time']}")
            print(f"   Stage 2 Time: {stage2_result['processing_time']}")
            print(f"   Images Generated: {len(stage2_result['data']['generated_images'])}")
            
            return result
            
        except Exception as e:
            print(f"❌ Pipeline Error: {e}")
            return self._create_error_result("Pipeline execution failed", str(e))
    
    def _execute_stage1_ocr(self, card_image_path: str) -> Dict:
        """
        Execute Stage 1: OCR Processing with Gemini 2.5 Flash
        
        Features:
        - Image preprocessing (resize, enhance, unsharp mask)
        - Deterministic API settings (temperature=0.0, topK=1, topP=0.1)
        - Enhanced OCR prompts for maximum accuracy
        - Structured JSON output parsing
        """
        
        print(f"\n📝 STAGE 1: OCR Processing (Gemini 2.5 Flash)")
        print(f"   Input: {Path(card_image_path).name}")
        
        stage1_start_time = time.time()
        
        try:
            # Validate input
            if not Path(card_image_path).exists():
                raise FileNotFoundError(f"Card image not found: {card_image_path}")
            
            # Execute OCR with enhanced preprocessing and prompts
            print("   🔍 Extracting text with maximum precision...")
            card_info = self.ocr_service.extract_text_from_card(card_image_path)
            
            if not card_info:
                raise ValueError("OCR returned no data")
            
            # Validate extracted data
            required_fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address']
            validated_info = {}
            
            for field in required_fields:
                validated_info[field] = card_info.get(field, '')
            
            stage1_end_time = time.time()
            processing_time = f"{stage1_end_time - stage1_start_time:.2f}s"
            
            result = {
                'success': True,
                'stage': 'OCR Processing',
                'model': 'Gemini 2.5 Flash',
                'processing_time': processing_time,
                'data': validated_info,
                'metadata': {
                    'image_path': card_image_path,
                    'preprocessing_applied': True,
                    'api_settings': 'Deterministic (temp=0.0, topK=1, topP=0.1)',
                    'fields_extracted': len([v for v in validated_info.values() if v])
                }
            }
            
            self.stage1_result = result
            
            print(f"   ✅ Stage 1 Completed: {processing_time}")
            print(f"   📊 Fields Extracted: {result['metadata']['fields_extracted']}/7")
            print(f"   👤 Name: {validated_info.get('name', 'N/A')}")
            print(f"   🏢 Company: {validated_info.get('company', 'N/A')}")
            print(f"   📧 Email: {validated_info.get('email', 'N/A')}")
            
            return result
            
        except Exception as e:
            stage1_end_time = time.time()
            processing_time = f"{stage1_end_time - stage1_start_time:.2f}s"
            
            print(f"   ❌ Stage 1 Failed: {e} ({processing_time})")
            
            return {
                'success': False,
                'stage': 'OCR Processing',
                'model': 'Gemini 2.5 Flash',
                'processing_time': processing_time,
                'error': str(e),
                'fallback_used': True
            }
    
    def _execute_stage2_generation(self, face_image_path: str, card_info: Dict, 
                                 prompt_template: str) -> Dict:
        """
        Execute Stage 2: AI Image Generation with Gemini 2.0 Flash Preview
        
        Features:
        - Multimodal input (prompt + OCR data + face image)
        - Multiple image variants generation
        - Proper metadata and session management
        - Error handling with fallback mechanisms
        """
        
        print(f"\n🎨 STAGE 2: AI Image Generation (Gemini 2.0 Flash Preview)")
        print(f"   Face Image: {Path(face_image_path).name}")
        print(f"   Style Template: {prompt_template}")
        print(f"   Person: {card_info.get('name', 'Unknown')}")
        
        stage2_start_time = time.time()
        
        try:
            # Validate inputs
            if not Path(face_image_path).exists():
                raise FileNotFoundError(f"Face image not found: {face_image_path}")
            
            # Prepare AI configuration
            ai_config = self.config.copy()
            ai_config['prompt_template'] = prompt_template
            
            print(f"   🤖 AI Config: Model={ai_config['model']}, Template={prompt_template}")
            
            # Execute AI image generation
            print("   🎯 Generating AI images with multimodal input...")
            generation_result = self.ai_generator.generate_dollhouse_image(
                face_image_path,
                card_info,
                ai_config
            )
            
            if not generation_result or not generation_result.get('success'):
                raise ValueError(f"AI generation failed: {generation_result.get('error', 'Unknown error')}")
            
            stage2_end_time = time.time()
            processing_time = f"{stage2_end_time - stage2_start_time:.2f}s"
            
            result = {
                'success': True,
                'stage': 'AI Image Generation',
                'model': 'Gemini 2.0 Flash Preview Image Generation',
                'processing_time': processing_time,
                'data': generation_result,
                'metadata': {
                    'face_image_path': face_image_path,
                    'prompt_template': prompt_template,
                    'multimodal_inputs': ['text_prompt', 'face_image', 'card_info'],
                    'variants_generated': len(generation_result.get('generated_images', [])),
                    'api_model': 'gemini-2.0-flash-preview-image-generation'
                }
            }
            
            self.stage2_result = result
            
            print(f"   ✅ Stage 2 Completed: {processing_time}")
            print(f"   🖼️ Images Generated: {result['metadata']['variants_generated']}")
            print(f"   📁 Output Directory: outputs/")
            
            return result
            
        except Exception as e:
            stage2_end_time = time.time()
            processing_time = f"{stage2_end_time - stage2_start_time:.2f}s"
            
            print(f"   ❌ Stage 2 Failed: {e} ({processing_time})")
            
            return {
                'success': False,
                'stage': 'AI Image Generation',
                'model': 'Gemini 2.0 Flash Preview Image Generation',
                'processing_time': processing_time,
                'error': str(e),
                'fallback_attempted': True
            }
    
    def _create_error_result(self, message: str, error: str) -> Dict:
        """Create standardized error result"""
        
        if self.current_session:
            self.current_session['status'] = 'error'
            self.current_session['error'] = error
        
        return {
            'success': False,
            'error': message,
            'details': error,
            'session_id': self.current_session['session_id'] if self.current_session else None,
            'stage1_result': self.stage1_result,
            'stage2_result': self.stage2_result
        }
    
    def get_pipeline_status(self) -> Dict:
        """Get current pipeline status and progress"""
        
        if not self.current_session:
            return {'status': 'idle', 'message': 'No active pipeline session'}
        
        return {
            'session_id': self.current_session['session_id'],
            'status': self.current_session['status'],
            'stage1_completed': self.stage1_result is not None,
            'stage2_completed': self.stage2_result is not None,
            'current_session': self.current_session
        }


def test_ai_pipeline():
    """Test the two-stage AI pipeline"""
    
    print("🧪 Testing Two-Stage AI Pipeline")
    print("=" * 50)
    
    pipeline = AIProcessingPipeline()
    
    # Test with sample images
    card_path = "test_card.jpg"
    face_path = "test_face.jpg"
    
    if Path(card_path).exists() and Path(face_path).exists():
        result = pipeline.process_business_card(
            card_image_path=card_path,
            face_image_path=face_path,
            prompt_template='cartoon'
        )
        
        if result['success']:
            print("\n✅ Pipeline Test Successful!")
            print(f"   Session: {result['session_id']}")
            print(f"   Total Time: {result['total_processing_time']}")
            print(f"   Images: {result['pipeline_summary']['images_generated']}")
        else:
            print(f"\n❌ Pipeline Test Failed: {result['error']}")
    else:
        print(f"❌ Test files not found: {card_path}, {face_path}")


if __name__ == "__main__":
    test_ai_pipeline()
