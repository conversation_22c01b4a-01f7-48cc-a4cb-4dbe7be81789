"""
API Controller - X<PERSON> lý các endpoint API và tích hợp bên ngoài
Handle API endpoints and external integrations
"""

from flask import Blueprint, jsonify, request, send_from_directory  # Flask components
from pathlib import Path  # Thư viện xử lý đường dẫn
import os  # Th<PERSON> viện hệ điều hành

from ai_config import get_available_prompts  # L<PERSON>y danh sách prompt có sẵn
from models.session_model import SessionModel  # Model session


class APIController:
    """Controller cho các endpoint API - Controller for API endpoints"""

    def __init__(self):
        """Khởi tạo API Controller"""
        self.blueprint = Blueprint('api', __name__, url_prefix='/api')  # Tạo Blueprint với prefix /api
        self.setup_routes()  # Thiết lập routes
        self.session_model = SessionModel()  # Khởi tạo model session
        print("🔌 API Controller initialized")

    def setup_routes(self):
        """Thiết lập các routes API - Setup API routes"""
        # Route lấy danh sách prompt templates
        self.blueprint.add_url_rule('/prompts', 'get_prompts', self.get_prompts, methods=['GET'])
        # Route lấy thông tin session hiện tại
        self.blueprint.add_url_rule('/session', 'get_session', self.get_session, methods=['GET'])
        # Route cập nhật session
        self.blueprint.add_url_rule('/session', 'update_session', self.update_session, methods=['POST'])
        # Route kiểm tra sức khỏe hệ thống
        self.blueprint.add_url_rule('/health', 'health_check', self.health_check, methods=['GET'])
        # Route tải file
        self.blueprint.add_url_rule('/download/<path:filename>', 'download_file', self.download_file, methods=['GET'])

    def get_prompts(self):
        """Endpoint API để lấy các template prompt có sẵn - API endpoint to get available prompt templates"""
        try:
            prompts = get_available_prompts()  # Lấy danh sách prompt
            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'prompts': prompts,  # Danh sách prompt
                'count': len(prompts)  # Số lượng prompt
            })
        except Exception as e:
            print(f"❌ Error getting prompts: {e}")
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to get prompts: {str(e)}'  # Thông báo lỗi
            }), 500

    def get_session(self):
        """Endpoint API để lấy thông tin session hiện tại - API endpoint to get current session information"""
        try:
            status = self.session_model.get_session_status()  # Lấy trạng thái session
            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'session': status  # Dữ liệu session
            })
        except Exception as e:
            print(f"❌ Error getting session: {e}")  # Log lỗi
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to get session: {str(e)}'  # Thông báo lỗi
            }), 500  # HTTP 500 Internal Server Error

    def update_session(self):
        """Endpoint API để cập nhật dữ liệu session - API endpoint to update session data"""
        try:
            if not request.is_json:  # Nếu request không phải JSON
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'Request must be JSON'  # Thông báo yêu cầu JSON
                }), 400  # HTTP 400 Bad Request

            data = request.get_json()  # Lấy dữ liệu JSON từ request

            # Đảm bảo session tồn tại - Ensure session exists
            if not self.session_model.current_session:  # Nếu chưa có session
                session_id = self.session_model.create_session()  # Tạo session mới
                print(f"Created new session: {session_id}")  # Log session mới

            # Cập nhật session với dữ liệu được cung cấp - Update session with provided data
            success = self.session_model.update_session(**data)  # Gọi hàm update session

            if success:  # Nếu cập nhật thành công
                return jsonify({
                    'status': 'success',  # Trạng thái thành công
                    'message': 'Session updated successfully',  # Thông báo thành công
                    'session': self.session_model.get_session_status()  # Trạng thái session mới
                })
            else:  # Nếu cập nhật thất bại
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'Failed to update session'  # Thông báo thất bại
                }), 500  # HTTP 500 Internal Server Error

        except Exception as e:
            print(f"❌ Error updating session: {e}")  # Log lỗi
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to update session: {str(e)}'  # Thông báo lỗi chi tiết
            }), 500  # HTTP 500 Internal Server Error

    def health_check(self):
        """Endpoint API để kiểm tra sức khỏe hệ thống - API endpoint for health check"""
        try:
            # Kiểm tra các thư mục thiết yếu có tồn tại không - Check if essential directories exist
            directories = ['static/img', 'sessions', 'prompts', 'outputs']  # Danh sách thư mục cần thiết
            missing_dirs = []  # Danh sách thư mục bị thiếu

            for directory in directories:  # Lặp qua từng thư mục
                if not Path(directory).exists():  # Nếu thư mục không tồn tại
                    missing_dirs.append(directory)  # Thêm vào danh sách thiếu

            # Kiểm tra file prompt có tồn tại không - Check if prompt files exist
            prompts_dir = Path('prompts')  # Đường dẫn thư mục prompts
            prompt_files = list(prompts_dir.glob('*.txt')) if prompts_dir.exists() else []  # Danh sách file prompt

            # Tạo đối tượng trạng thái sức khỏe - Create health status object
            health_status = {
                'status': 'healthy' if not missing_dirs else 'warning',  # Trạng thái: khỏe mạnh hoặc cảnh báo
                'timestamp': self._get_current_timestamp(),  # Thời gian kiểm tra
                'directories': {  # Thông tin thư mục
                    'missing': missing_dirs,  # Danh sách thư mục bị thiếu
                    'total_checked': len(directories)  # Tổng số thư mục đã kiểm tra
                },
                'prompts': {  # Thông tin prompt
                    'available': len(prompt_files),  # Số lượng file prompt có sẵn
                    'files': [f.name for f in prompt_files]  # Danh sách tên file prompt
                },
                'session': self.session_model.get_session_status()  # Trạng thái session hiện tại
            }

            # Xác định status code dựa trên kết quả kiểm tra - Determine status code based on check results
            status_code = 200 if not missing_dirs else 206  # 200 OK hoặc 206 Partial Content

            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'health': health_status  # Dữ liệu sức khỏe hệ thống
            }), status_code  # Trả về với status code phù hợp

        except Exception as e:
            print(f"❌ Health check error: {e}")  # Log lỗi health check
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Health check failed: {str(e)}',  # Thông báo lỗi chi tiết
                'health': {  # Thông tin sức khỏe khi có lỗi
                    'status': 'unhealthy',  # Trạng thái không khỏe mạnh
                    'timestamp': self._get_current_timestamp()  # Thời gian xảy ra lỗi
                }
            }), 500  # HTTP 500 Internal Server Error

    def download_file(self, filename):
        """Endpoint API để tải file - API endpoint to download files"""
        try:
            # Kiểm tra bảo mật - chỉ cho phép tải từ thư mục cụ thể - Security check - only allow downloads from specific directories
            allowed_dirs = ['outputs', 'static/img']  # Danh sách thư mục được phép
            file_path = None  # Đường dẫn file được tìm thấy

            # Tìm file trong các thư mục được phép - Search for file in allowed directories
            for directory in allowed_dirs:  # Lặp qua từng thư mục được phép
                potential_path = Path(directory) / filename  # Tạo đường dẫn tiềm năng
                if potential_path.exists() and potential_path.is_file():  # Nếu file tồn tại và là file
                    file_path = potential_path  # Lưu đường dẫn file
                    break  # Thoát khỏi vòng lặp

            if not file_path:  # Nếu không tìm thấy file
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'File not found'  # Thông báo không tìm thấy file
                }), 404  # HTTP 404 Not Found

            # Gửi file - Send file
            return send_from_directory(
                file_path.parent,  # Thư mục chứa file
                file_path.name,  # Tên file
                as_attachment=True,  # Gửi dưới dạng attachment
                download_name=filename  # Tên file khi tải về
            )

        except Exception as e:
            print(f"❌ Download error: {e}")  # Log lỗi tải file
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Download failed: {str(e)}'  # Thông báo lỗi chi tiết
            }), 500  # HTTP 500 Internal Server Error

    def _get_current_timestamp(self):
        """Lấy timestamp hiện tại theo định dạng ISO - Get current timestamp in ISO format"""
        from datetime import datetime  # Import datetime để lấy thời gian
        return datetime.now().isoformat()  # Trả về thời gian hiện tại theo định dạng ISO


class MainController:
    """Controller chính cho các route cơ bản - Main controller for basic routes"""

    def __init__(self):
        """Khởi tạo Main Controller"""
        self.blueprint = Blueprint('main', __name__)  # Tạo Blueprint cho main routes
        self.setup_routes()  # Thiết lập các routes
        self.session_model = SessionModel()  # Khởi tạo model session
        print("🏠 Main Controller initialized")  # Log khởi tạo thành công

    def setup_routes(self):
        """Thiết lập các routes chính - Setup main routes"""
        # Route trang chủ
        self.blueprint.add_url_rule('/', 'index', self.index)
        # Route phục vụ file static
        self.blueprint.add_url_rule('/static/<path:filename>', 'static_files', self.static_files)
        # Route phục vụ file output
        self.blueprint.add_url_rule('/outputs/<path:filename>', 'output_files', self.output_files)

    def index(self):
        """Trang chủ chính - Main index page"""
        try:
            from flask import render_template  # Import để render template

            # Đảm bảo session tồn tại - Ensure session exists
            if not self.session_model.current_session:  # Nếu chưa có session
                session_id = self.session_model.create_session()  # Tạo session mới
                print(f"Created new session for index: {session_id}")  # Log session mới

            return render_template('index.html')  # Render trang index.html

        except Exception as e:
            print(f"❌ Index error: {e}")  # Log lỗi trang chủ
            return f"Error loading page: {str(e)}", 500  # Trả về lỗi 500

    def _get_relative_path(self, file_path):
        """Chuyển đổi đường dẫn tuyệt đối thành đường dẫn tương đối để phục vụ web - Convert absolute path to relative path for web serving"""
        if not file_path:  # Nếu không có đường dẫn
            return None  # Trả về None

        # Chuyển đổi thành đối tượng Path - Convert to Path object
        path = Path(file_path)

        # Nếu đã là đường dẫn tương đối đến static, trả về như vậy - If it's already relative to static, return as is
        if 'static' in str(path):  # Nếu chứa 'static'
            return str(path).replace('\\', '/')  # Thay thế backslash bằng forward slash

        # Nếu trong thư mục outputs, trả về đường dẫn tương đối - If it's in outputs folder, return relative path
        if 'outputs' in str(path):  # Nếu chứa 'outputs'
            return str(path).replace('\\', '/')  # Thay thế backslash bằng forward slash

        # Mặc định: chỉ trả về tên file - Default: return filename only
        return path.name  # Trả về tên file

    def static_files(self, filename):
        """Phục vụ file static - Serve static files"""
        try:
            from flask import send_from_directory  # Import để phục vụ file
            return send_from_directory('static', filename)  # Phục vụ file từ thư mục static
        except Exception as e:
            print(f"❌ Static file error: {e}")  # Log lỗi file static
            return "File not found", 404  # Trả về lỗi 404

    def output_files(self, filename):
        """Phục vụ file output - Serve output files"""
        try:
            from flask import send_from_directory  # Import để phục vụ file
            print(f"📁 Serving output file: {filename}")  # Log file đang phục vụ
            return send_from_directory('outputs', filename)  # Phục vụ file từ thư mục outputs
        except Exception as e:
            print(f"❌ Output file error: {e}")  # Log lỗi file output
            return "File not found", 404  # Trả về lỗi 404


# Tạo các instance controller toàn cục - Create global controller instances
api_controller = APIController()  # Instance controller API
main_controller = MainController()  # Instance controller chính

def get_api_blueprint():
    """Lấy blueprint của API controller - Get API controller blueprint"""
    return api_controller.blueprint  # Trả về blueprint API

def get_main_blueprint():
    """Lấy blueprint của main controller - Get main controller blueprint"""
    return main_controller.blueprint  # Trả về blueprint chính
