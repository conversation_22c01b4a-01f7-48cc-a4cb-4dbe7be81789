"""
Helper utilities for AI_Gen application
"""

import os
import json
import uuid
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from PIL import Image
import base64
from typing import Dict, List, Optional, Union


def ensure_directories(*directories):
    """Ensure that specified directories exist"""
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def generate_unique_id():
    """Generate a unique identifier"""
    return str(uuid.uuid4())


def generate_timestamp_id():
    """Generate timestamp-based ID"""
    return datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]


def get_file_hash(file_path: Union[str, Path]) -> str:
    """Get MD5 hash of a file"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"❌ Error calculating file hash: {e}")
        return ""


def validate_image_file(file_path: Union[str, Path]) -> bool:
    """Validate if file is a valid image"""
    try:
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception:
        return False


def get_image_info(file_path: Union[str, Path]) -> Dict:
    """Get image information"""
    try:
        with Image.open(file_path) as img:
            return {
                'width': img.width,
                'height': img.height,
                'format': img.format,
                'mode': img.mode,
                'size_bytes': Path(file_path).stat().st_size
            }
    except Exception as e:
        print(f"❌ Error getting image info: {e}")
        return {}


def resize_image(input_path: Union[str, Path], output_path: Union[str, Path], 
                max_width: int = 1024, max_height: int = 1024, quality: int = 85) -> bool:
    """Resize image while maintaining aspect ratio"""
    try:
        with Image.open(input_path) as img:
            # Calculate new size
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Save resized image
            img.save(output_path, optimize=True, quality=quality)
            return True
    except Exception as e:
        print(f"❌ Error resizing image: {e}")
        return False


def encode_image_to_base64(file_path: Union[str, Path]) -> Optional[str]:
    """Encode image file to base64 string"""
    try:
        with open(file_path, 'rb') as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"❌ Error encoding image to base64: {e}")
        return None


def decode_base64_to_image(base64_string: str, output_path: Union[str, Path]) -> bool:
    """Decode base64 string to image file"""
    try:
        image_data = base64.b64decode(base64_string)
        with open(output_path, 'wb') as image_file:
            image_file.write(image_data)
        return True
    except Exception as e:
        print(f"❌ Error decoding base64 to image: {e}")
        return False


def clean_filename(filename: str) -> str:
    """Clean filename by removing invalid characters"""
    import re
    # Remove invalid characters
    cleaned = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple underscores
    cleaned = re.sub(r'_+', '_', cleaned)
    # Remove leading/trailing underscores
    cleaned = cleaned.strip('_')
    return cleaned


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def get_relative_path(absolute_path: Union[str, Path], base_path: Union[str, Path] = None) -> str:
    """Convert absolute path to relative path"""
    if not absolute_path:
        return ""
    
    path = Path(absolute_path)
    
    if base_path:
        try:
            return str(path.relative_to(Path(base_path)))
        except ValueError:
            pass
    
    # If path contains 'static', return from static onwards
    if 'static' in path.parts:
        static_index = path.parts.index('static')
        return str(Path(*path.parts[static_index:]))
    
    return str(path)


def load_json_file(file_path: Union[str, Path]) -> Optional[Dict]:
    """Load JSON file safely"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading JSON file {file_path}: {e}")
        return None


def save_json_file(data: Dict, file_path: Union[str, Path]) -> bool:
    """Save data to JSON file safely"""
    try:
        # Ensure directory exists
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"❌ Error saving JSON file {file_path}: {e}")
        return False


def cleanup_old_files(directory: Union[str, Path], days_old: int = 7, 
                     file_pattern: str = "*") -> int:
    """Clean up old files in directory"""
    try:
        directory = Path(directory)
        if not directory.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
        cleaned_count = 0
        
        for file_path in directory.glob(file_pattern):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                file_path.unlink()
                cleaned_count += 1
        
        return cleaned_count
        
    except Exception as e:
        print(f"❌ Error cleaning up files: {e}")
        return 0


def validate_card_info(card_info: Dict) -> Dict:
    """Validate and clean card information"""
    required_fields = ['name', 'occupation', 'company', 'gmail', 'phone_number']
    
    # Ensure all required fields exist
    for field in required_fields:
        if field not in card_info or not card_info[field]:
            card_info[field] = 'N/A'
    
    # Clean and validate fields
    card_info['name'] = str(card_info['name']).strip()
    card_info['occupation'] = str(card_info['occupation']).strip()
    card_info['company'] = str(card_info['company']).strip()
    card_info['gmail'] = str(card_info['gmail']).strip().lower()
    card_info['phone_number'] = str(card_info['phone_number']).strip()
    
    return card_info


def format_timestamp(timestamp: Union[str, datetime], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format timestamp to string"""
    try:
        if isinstance(timestamp, str):
            # Try to parse ISO format
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        elif isinstance(timestamp, datetime):
            dt = timestamp
        else:
            return str(timestamp)
        
        return dt.strftime(format_str)
    except Exception:
        return str(timestamp)


def get_system_info() -> Dict:
    """Get basic system information"""
    import platform
    import psutil
    
    try:
        return {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total,
            'memory_available': psutil.virtual_memory().available,
            'disk_usage': psutil.disk_usage('.').percent
        }
    except Exception as e:
        print(f"❌ Error getting system info: {e}")
        return {}


class Logger:
    """Simple logger utility"""
    
    def __init__(self, name: str = "AI_Gen"):
        self.name = name
    
    def log(self, level: str, message: str):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{self.name}] {level.upper()}: {message}")
    
    def info(self, message: str):
        self.log("info", message)
    
    def warning(self, message: str):
        self.log("warning", message)
    
    def error(self, message: str):
        self.log("error", message)
    
    def debug(self, message: str):
        self.log("debug", message)


# Create default logger instance
logger = Logger()
