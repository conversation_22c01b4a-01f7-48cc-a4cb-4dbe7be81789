"""
C<PERSON><PERSON> hình <PERSON>ng dụng AI_Gen - Configuration settings for AI_Gen application
File này chứa tất cả các thiết lập cấu hình cho ứng dụng
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Tải các biến môi trường từ file .env - Load environment variables
load_dotenv()

class Config:
    """Lớp cấu hình cơ bản - Base configuration class"""

    # Thiết lập Flask - Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'  # Khóa bí mật cho session
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'  # Chế độ debug

    # Thiết lập ứng dụng - Application settings
    UPLOAD_FOLDER = 'static/img'  # Thư mục lưu ảnh gốc được chụp/upload
    SESSION_FOLDER = 'sessions'  # Giữ để tương thích nhưng không còn sử dụng - Keep for compatibility but no longer used
    OUTPUT_FOLDER = 'outputs'  # Thư mục lưu file đã tổ chức theo tên người
    PROMPTS_FOLDER = 'prompts'  # Thư mục chứa các template prompt cho AI

    # Đảm bảo các thư mục tồn tại - Ensure directories exist
    @classmethod
    def init_app(cls, app):
        """Khởi tạo ứng dụng với cấu hình - Initialize application with config"""
        # Tạo các thư mục cần thiết (không tạo thư mục sessions nữa) - Create necessary directories (sessions folder no longer created)
        for folder in [cls.UPLOAD_FOLDER, cls.OUTPUT_FOLDER]:
            Path(folder).mkdir(exist_ok=True)  # Tạo thư mục nếu chưa tồn tại

        # Thiết lập cấu hình Flask - Set Flask config
        app.config['UPLOAD_FOLDER'] = cls.UPLOAD_FOLDER  # Đường dẫn thư mục upload
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # Giới hạn kích thước file tối đa 16MB - 16MB max file size

class DevelopmentConfig(Config):
    """Cấu hình môi trường phát triển - Development configuration"""
    DEBUG = True  # Bật chế độ debug

class ProductionConfig(Config):
    """Cấu hình môi trường sản xuất - Production configuration"""
    DEBUG = False  # Tắt chế độ debug

class TestingConfig(Config):
    """Cấu hình môi trường test - Testing configuration"""
    TESTING = True  # Bật chế độ testing
    DEBUG = True  # Bật chế độ debug

# Ánh xạ cấu hình - Configuration mapping
config = {
    'development': DevelopmentConfig,  # Môi trường phát triển
    'production': ProductionConfig,    # Môi trường sản xuất
    'testing': TestingConfig,          # Môi trường test
    'default': DevelopmentConfig       # Mặc định là môi trường phát triển
}

# Cấu hình dịch vụ AI - AI Service Configuration
class AIConfig:
    """Cấu hình dịch vụ AI - AI service configuration"""

    # Thiết lập Gemini API - Gemini API settings
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')  # API key từ biến môi trường
    GEMINI_OCR_MODEL = 'gemini-2.5-flash'  # Model AI cho OCR (đọc thông tin name card)
    GEMINI_IMAGE_MODEL = 'gemini-2.0-flash-preview-image-generation'  # Model AI cho tạo ảnh

    # Thiết lập tạo ảnh - Generation settings
    DEFAULT_PROMPT_TEMPLATE = 'prompt'  # Template prompt mặc định
    MAX_GENERATION_TIME = 120  # Thời gian tối đa để tạo ảnh (giây) - seconds
    MAX_RETRIES = 3  # Số lần thử lại tối đa khi tạo ảnh thất bại

    # Thiết lập ảnh - Image settings
    OUTPUT_IMAGE_FORMAT = 'PNG'  # Định dạng ảnh đầu ra
    OUTPUT_IMAGE_QUALITY = 95  # Chất lượng ảnh (0-100)
    MAX_IMAGE_SIZE = (2048, 2048)  # Kích thước ảnh tối đa (width, height)

# Cấu hình Camera - Camera Configuration
class CameraConfig:
    """Cấu hình Camera - Camera configuration"""

    # Thiết lập camera - Camera settings
    CAMERA_WIDTH = 640  # Độ rộng khung hình camera
    CAMERA_HEIGHT = 480  # Độ cao khung hình camera
    CAMERA_FPS = 60  # Tốc độ khung hình 60 FPS như yêu cầu - Yêu cầu 60 FPS như project_directory

    # Thiết lập phát hiện - Detection settings
    FACE_DETECTION_ENABLED = True  # Bật tính năng phát hiện khuôn mặt
    CARD_DETECTION_ENABLED = True  # Bật tính năng phát hiện name card

    # Thiết lập chụp ảnh - Capture settings
    CAPTURE_FORMAT = 'JPEG'  # Định dạng ảnh chụp
    CAPTURE_QUALITY = 95  # Chất lượng ảnh chụp (0-100)
