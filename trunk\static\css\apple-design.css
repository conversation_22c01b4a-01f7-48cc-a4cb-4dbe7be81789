/* 
 * AI_Gen - Apple Design System
 * Inspired by Apple's design philosophy: Simplicity, Elegance, Functionality
 */

/* ===== VIBRANT DESIGN TOKENS ===== */
:root {
  /* Vibrant Color Palette - Bắt mắt và tươi sáng */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Background Gradients */
  --bg-main: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --bg-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --bg-card: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  --bg-surface: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Vibrant Colors */
  --color-primary: #667eea;
  --color-secondary: #f093fb;
  --color-accent: #4facfe;
  --color-success: #43e97b;
  --color-warning: #fee140;
  --color-danger: #fa709a;

  /* Text Colors */
  --color-text: #2d3748;
  --color-text-secondary: #4a5568;
  --color-text-light: #718096;
  --color-text-white: #10053a;
  --color-background: #ffffff;
  --color-surface: #f7fafc;
  
  /* Typography - Vietnamese-supported fonts */
  --font-family: 'Nunito', 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Enhanced spacing system */
  --spacing-xs: 6px;
  --spacing-sm: 12px;
  --spacing-md: 20px;
  --spacing-lg: 32px;
  --spacing-xl: 48px;
  --spacing-2xl: 64px;
  --spacing-3xl: 80px;
  --spacing-4xl: 96px;
  
  /* Border radius - Enhanced rounded corners */
  --radius-sm: 12px;
  --radius-md: 18px;
  --radius-lg: 24px;
  --radius-xl: 32px;
  --radius-2xl: 40px;
  --radius-3xl: 48px;
  
  /* Shadows - Apple's subtle depth */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  
  /* Transitions - Apple's fluid animations */
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  color: var(--color-text);
  background: var(--bg-main);
  background-attachment: fixed;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* ===== APPLE-STYLE LAYOUT ===== */
.apple-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* ===== NAVIGATION (Vibrant style) ===== */
.apple-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  transition: var(--transition);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.apple-nav-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.apple-logo {
  font-size: 20px;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  text-decoration: none;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.apple-nav-links {
  display: flex;
  gap: var(--spacing-xl);
  list-style: none;
}

.apple-nav-link {
  font-size: 16px;
  color: var(--color-text-white);
  text-decoration: none;
  transition: var(--transition);
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.apple-nav-link:hover {
  color: #fff;
  text-shadow: 0 0 10px rgba(255,255,255,0.8);
  transform: translateY(-1px);
}

/* ===== HERO SECTION (Vibrant style) ===== */
.apple-hero {
  padding: 140px 0 100px;
  text-align: center;
  background: var(--bg-hero);
  position: relative;
  overflow: hidden;
}

.apple-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
  pointer-events: none;
}

.apple-hero-title {
  font-size: clamp(40px, 6vw, 72px);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-white);
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.apple-hero-subtitle {
  font-size: 24px;
  font-weight: var(--font-weight-medium);
  color: rgba(255,255,255,0.9);
  margin-bottom: var(--spacing-2xl);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
  position: relative;
  z-index: 1;
}

/* ===== CARDS (Vibrant style) ===== */
.apple-card {
  background: var(--bg-card);
  border-radius: var(--radius-2xl);
  box-shadow: 0 10px 40px rgba(102, 126, 234, 0.2);
  overflow: hidden;
  transition: var(--transition);
  border: 2px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20px);
  margin: var(--spacing-lg);
}

.apple-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
  border-color: rgba(255,255,255,0.5);
}

.apple-card-header {
  padding: var(--spacing-xl);
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.apple-card-title {
  font-size: 22px;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  font-family: 'Poppins', sans-serif;
}

.apple-card-subtitle {
  font-size: 16px;
  color: var(--color-text-secondary);
  font-family: 'Inter', sans-serif;
}

.apple-card-body {
  padding: var(--spacing-2xl);
}

/* ===== BUTTONS (Vibrant style) ===== */
.apple-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: 20px 40px;
  font-size: 18px;
  font-weight: var(--font-weight-bold);
  border: none;
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  min-height: 64px;
  position: relative;
  overflow: hidden;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Poppins', sans-serif;
  margin: var(--spacing-sm);
}

.apple-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.apple-button:hover::before {
  left: 100%;
}

.apple-button-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.apple-button-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
}

.apple-button-secondary {
  background: var(--secondary-gradient);
  color: white;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.apple-button-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(240, 147, 251, 0.6);
}

/* ===== ENHANCED CAMERA INTERFACE ===== */
.camera-main-container {
  background: var(--bg-card);
  border-radius: var(--radius-3xl);
  padding: var(--spacing-2xl);
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
  border: 3px solid rgba(255,255,255,0.4);
  backdrop-filter: blur(30px);
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
}

.camera-section-header {
  margin-bottom: var(--spacing-xl);
}

.camera-grid-enhanced {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin: 0;
  padding: 0;
  width: 100%;
}

.camera-container-enhanced {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-lg);
  border: 2px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  height: 100%;
  min-height: 500px;
}

.camera-container-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
  border-color: rgba(255,255,255,0.4);
}

.camera-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid rgba(255,255,255,0.1);
}

.camera-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.camera-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.camera-title-text h3 {
  font-size: 20px;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 4px;
}

.camera-title-text p {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0;
}

.status-indicator-enhanced {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255,255,255,0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.camera-frame-enhanced {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: #000;
  border: 3px solid rgba(255,255,255,0.3);
  box-shadow: 0 12px 32px rgba(0,0,0,0.3);
}

.camera-stream-enhanced {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Support both img and video elements */
.camera-stream-enhanced video,
.camera-stream-enhanced img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.camera-overlay-guides {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.guide-corner {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid var(--color-accent);
  opacity: 0.8;
}

.guide-corner.tl {
  top: 20px;
  left: 20px;
  border-right: none;
  border-bottom: none;
}

.guide-corner.tr {
  top: 20px;
  right: 20px;
  border-left: none;
  border-bottom: none;
}

.guide-corner.bl {
  bottom: 20px;
  left: 20px;
  border-right: none;
  border-top: none;
}

.guide-corner.br {
  bottom: 20px;
  right: 20px;
  border-left: none;
  border-top: none;
}

.camera-overlay-face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.face-guide-oval {
  width: 180px;
  height: 220px;
  border: 3px solid var(--color-secondary);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  opacity: 0.8;
  animation: faceGuidePulse 2s ease-in-out infinite;
}

@keyframes faceGuidePulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.apple-camera-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
  transition: var(--transition);
  border: 2px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20px);
  margin: 0;
  height: 100%;
}

.apple-camera-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(102, 126, 234, 0.3);
  border-color: rgba(255,255,255,0.5);
}

.apple-camera-header {
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255,255,255,0.2);
  min-height: 60px;
}

.apple-camera-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.apple-camera-icon {
  font-size: 24px;
}

.apple-camera-title {
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 2px;
}

.apple-camera-subtitle {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.apple-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--apple-blue);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.apple-camera-display {
  position: relative;
  background: #000;
  overflow: hidden;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  flex: 1;
  min-height: 600px;
  height: 70vh;
}

.apple-camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* ===== CAMERA SCANNING WAVE EFFECT ===== */
.camera-scan-wave {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(67, 233, 123, 0.8) 20%,
    rgba(56, 249, 215, 1) 50%,
    rgba(67, 233, 123, 0.8) 80%,
    transparent 100%);
  animation: scan-wave 3s ease-in-out infinite;
  z-index: 10;
  box-shadow: 0 0 20px rgba(67, 233, 123, 0.6);
}

@keyframes scan-wave {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    top: calc(100% - 3px);
    opacity: 1;
  }
}

.camera-scan-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(67, 233, 123, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(67, 233, 123, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: grid-pulse 4s ease-in-out infinite;
  pointer-events: none;
  z-index: 5;
}

@keyframes grid-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.camera-scan-corners {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  pointer-events: none;
  z-index: 15;
}

.scan-corner {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(67, 233, 123, 0.9);
  animation: corner-pulse 2s ease-in-out infinite;
}

.scan-corner-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
  border-radius: var(--radius-sm) 0 0 0;
}

.scan-corner-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
  border-radius: 0 var(--radius-sm) 0 0;
}

.scan-corner-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 var(--radius-sm);
}

.scan-corner-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
  border-radius: 0 0 var(--radius-sm) 0;
}

@keyframes corner-pulse {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 10px rgba(67, 233, 123, 0.3);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(67, 233, 123, 0.6);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .apple-camera-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .apple-hero {
    padding: 80px 0 60px;
  }
  
  .apple-nav-links {
    display: none;
  }
}

/* ===== UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* ===== APPLE CAMERA ENHANCEMENTS ===== */
.apple-focus-guides {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  pointer-events: none;
}

.apple-guide-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.apple-guide-tl {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.apple-guide-tr {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.apple-guide-bl {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.apple-guide-br {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.apple-face-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.apple-face-oval {
  width: 200px;
  height: 250px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

.apple-camera-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: var(--spacing-lg);
  text-align: center;
}

.apple-status-text {
  color: white;
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

/* ===== VIBRANT FORM ELEMENTS ===== */
.apple-select {
  width: 100%;
  padding: 20px 24px;
  font-size: 18px;
  font-family: 'Inter', sans-serif;
  font-weight: var(--font-weight-medium);
  border: 3px solid rgba(255,255,255,0.4);
  border-radius: var(--radius-2xl);
  background: var(--bg-card);
  color: var(--color-text);
  transition: var(--transition);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 20px center;
  background-repeat: no-repeat;
  background-size: 24px;
  padding-right: 60px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.apple-select:focus {
  outline: none;
  border-color: rgba(67, 233, 123, 0.8);
  box-shadow: 0 0 0 4px rgba(67, 233, 123, 0.3);
  transform: translateY(-2px);
}

.apple-select:hover {
  border-color: rgba(255,255,255,0.6);
  transform: translateY(-1px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
}

.vibrant-select option {
  padding: 15px;
  font-size: 16px;
  font-weight: var(--font-weight-medium);
  background: var(--color-background);
  color: var(--color-text);
}

.vibrant-description {
  animation: glow 3s ease-in-out infinite;
  transition: var(--transition);
}

.vibrant-description:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 40px rgba(67, 233, 123, 0.3);
}

/* ===== APPLE BUTTON GROUP ===== */
.apple-button-group {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* ===== APPLE PROGRESS BAR ===== */
.apple-progress-bar {
  width: 100%;
  height: 4px;
  background: var(--color-surface);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.apple-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--apple-blue-dark));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  width: 0%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

/* ===== APPLE ANIMATIONS ===== */
.apple-fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.apple-scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

/* ===== APPLE LOADING STATES ===== */
.apple-loading {
  position: relative;
  overflow: hidden;
}

.apple-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== APPLE RIPPLE EFFECT ===== */
.apple-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== APPLE SPINNER ===== */
.apple-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.apple-spinner-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-primary);
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

.apple-spinner-dot:nth-child(1) { animation-delay: -0.32s; }
.apple-spinner-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ===== APPLE NOTIFICATIONS ===== */
.apple-notification {
  position: fixed;
  top: 80px;
  right: var(--spacing-lg);
  background: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-md) var(--spacing-lg);
  z-index: 1000;
  transform: translateX(100%);
  transition: var(--transition);
  border: 1px solid var(--color-surface);
}

.apple-notification-show {
  transform: translateX(0);
}

.apple-notification-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.apple-notification-icon {
  font-size: 18px;
}

.apple-notification-message {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.apple-notification-success {
  border-left: 4px solid var(--apple-blue);
}

.apple-notification-error {
  border-left: 4px solid #FF3B30;
}

.apple-notification-warning {
  border-left: 4px solid #FF9500;
}

.apple-notification-info {
  border-left: 4px solid var(--color-primary);
}

/* ===== APPLE IMAGE PREVIEW ===== */
.apple-image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.apple-image-preview-show {
  opacity: 1;
}

.apple-image-preview-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.apple-image-preview {
  max-width: 100%;
  max-height: 100%;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2xl);
}

.apple-image-preview-close {
  position: absolute;
  top: -40px;
  right: -40px;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.apple-image-preview-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===== VIBRANT ENHANCED ANIMATIONS ===== */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

.apple-float {
  animation: float 4s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
    filter: brightness(1);
  }
  50% {
    box-shadow: 0 0 50px rgba(240, 147, 251, 0.6);
    filter: brightness(1.1);
  }
}

.apple-glow {
  animation: glow 3s ease-in-out infinite;
}

@keyframes rainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

.apple-rainbow {
  animation: rainbow 10s linear infinite;
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.apple-pulse {
  animation: pulse-scale 2s ease-in-out infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.apple-gradient-animate {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

/* ===== APPLE FOCUS STATES ===== */
.apple-button:focus,
.apple-select:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.25);
}

/* ===== VIBRANT UTILITIES ===== */
.vibrant-text-glow {
  text-shadow: 0 0 20px currentColor;
}

.vibrant-border-glow {
  border: 2px solid;
  border-image: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c) 1;
  animation: rainbow 5s linear infinite;
}

.vibrant-bg-animated {
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.vibrant-hover-lift:hover {
  transform: translateY(-10px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.vibrant-shadow-colorful {
  box-shadow:
    0 10px 30px rgba(102, 126, 234, 0.3),
    0 20px 60px rgba(240, 147, 251, 0.2);
}

.vibrant-text-rainbow {
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 6s ease infinite;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 1200px) {
  .camera-grid-enhanced {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .camera-main-container {
    padding: var(--spacing-xl);
    margin: var(--spacing-md);
  }

  .camera-frame-enhanced {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .camera-main-container {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
    border-radius: var(--radius-2xl);
  }

  .camera-grid-enhanced {
    gap: var(--spacing-lg);
  }

  .camera-container-enhanced {
    min-height: 400px;
    padding: var(--spacing-md);
  }

  .camera-frame-enhanced {
    height: 280px;
  }

  .camera-header-enhanced {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .camera-title-section {
    justify-content: center;
  }

  .camera-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .face-guide-oval {
    width: 140px;
    height: 180px;
  }

  .guide-corner {
    width: 25px;
    height: 25px;
  }

  .apple-camera-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: 0;
    margin: 0;
  }

  .apple-camera-display {
    min-height: 250px;
  }

  .apple-hero {
    padding: 120px 0 80px;
  }

  .apple-hero-title {
    font-size: clamp(32px, 8vw, 52px);
  }

  .apple-hero-subtitle {
    font-size: 20px;
    padding: 0 var(--spacing-md);
  }

  .apple-button {
    padding: 18px 32px;
    font-size: 16px;
    margin: var(--spacing-xs);
  }

  .apple-nav {
    height: 56px;
  }

  .apple-nav-links {
    display: none;
  }

  .apple-card {
    margin: var(--spacing-md);
    border-radius: var(--radius-xl);
  }

  .apple-camera-card {
    margin: var(--spacing-md);
    border-radius: var(--radius-2xl);
  }

  .apple-result-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .apple-action-buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  .apple-button {
    width: 100%;
    max-width: 320px;
  }

  .apple-select {
    padding: 18px 20px;
    font-size: 16px;
    padding-right: 50px;
  }

  .vibrant-description {
    padding: var(--spacing-lg);
    font-size: 16px;
  }

  .camera-scan-corners {
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
  }

  .scan-corner {
    width: 25px;
    height: 25px;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.apple-camera-feed {
  will-change: transform;
  backface-visibility: hidden;
}

.apple-button {
  will-change: transform, box-shadow;
}

.apple-card {
  will-change: transform, box-shadow;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.apple-button:focus-visible,
.apple-select:focus-visible {
  outline: 3px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .apple-nav,
  .apple-button,
  .apple-camera-display {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
