"""
AI_Gen Application - Main Entry Point
Ứng dụng AI_Gen - Đ<PERSON><PERSON><PERSON> khởi đầu chính
MVC Architecture with Flask Blueprints
Kiến trúc MVC với Flask Blueprints
"""

import os  # Import thư viện hệ điều hành để làm việc với biến môi trường
import atexit  # Import thư viện để đăng ký hàm cleanup khi ứng dụng thoát
from flask import Flask  # Import Flask framework để tạo web application
from dotenv import load_dotenv  # Import để load biến môi trường từ file .env

# Load environment variables - Tải các biến môi trường từ file .env
load_dotenv()

# Import configuration - Import cấu hình ứng dụng
from config import config, Config

# Import controllers - Import các controller xử lý logic nghiệp vụ
from controllers.camera_controller import get_camera_blueprint, camera_controller  # Controller xử lý camera
from controllers.processing_controller import get_processing_blueprint  # Controller xử lý AI processing
from controllers.api_controller import get_api_blueprint, get_main_blueprint  # Controller xử lý API và routes chính

# Import utilities - Import các tiện ích hỗ trợ
from utils.helpers import ensure_directories, logger  # Hàm tạo thư mục và logger


def create_app(config_name='default'):
    """Application factory pattern - Mẫu thiết kế Factory để tạo ứng dụng"""

    # Create Flask app with custom template folder - Tạo ứng dụng Flask với thư mục template tùy chỉnh
    app = Flask(__name__, template_folder='views')

    # Load configuration - Tải cấu hình cho ứng dụng
    app.config.from_object(config[config_name])  # Áp dụng cấu hình từ config object
    Config.init_app(app)  # Khởi tạo cấu hình cho app

    # Ensure required directories exist - Đảm bảo các thư mục cần thiết tồn tại
    ensure_directories(
        'static/img', 'static/css', 'static/js', 'static/images',  # Thư mục static files
        'sessions', 'outputs', 'prompts', 'views'  # Thư mục dữ liệu và templates
    )

    # Register blueprints - Đăng ký các blueprint (module con) cho ứng dụng
    register_blueprints(app)

    # Setup error handlers - Thiết lập các handler xử lý lỗi
    setup_error_handlers(app)

    # Setup cleanup on exit - Thiết lập cleanup khi ứng dụng thoát
    setup_cleanup_handlers()
    
    # Add simple result route directly - Thêm route kết quả trực tiếp
    @app.route('/result')
    def result():
        """Simple result page with detailed debugging - Trang kết quả đơn giản với debug chi tiết"""
        print("🔍 DIRECT RESULT ROUTE ACCESSED")  # Log khi route được truy cập
        from flask import render_template  # Import render_template để render HTML
        from controllers.camera_controller import camera_controller  # Import camera controller

        try:
            # Lấy session hiện tại từ camera controller
            session = camera_controller.session_model.current_session
            print(f"📄 Session exists: {session is not None}")  # Log kiểm tra session tồn tại

            if session:  # Nếu có session
                # In thông tin debug về session
                print(f"📄 Session ID: {session.get('session_id')}")  # ID phiên làm việc
                print(f"📄 Session status: {session.get('status')}")  # Trạng thái phiên
                print(f"📄 Card info: {session.get('card_info')}")  # Thông tin thẻ
                print(f"📄 Card image: {session.get('card_image')}")  # Ảnh thẻ
                print(f"📄 Face image: {session.get('face_image')}")  # Ảnh khuôn mặt
                print(f"📄 Generated images: {session.get('generated_images')}")  # Ảnh đã tạo

                # Fix file paths for web serving - CHỈ LẤY 2 ẢNH MỚI NHẤT
                # Sửa đường dẫn file để phục vụ web - chỉ lấy 2 ảnh mới nhất
                all_generated_images = []  # Danh sách tất cả ảnh được tạo
                for image_info in session.get('generated_images', []):
                    all_generated_images.append(image_info)  # Thêm từng ảnh vào danh sách

                # CHỈ LẤY 2 ẢNH MỚI NHẤT - Chỉ hiển thị 2 ảnh cuối cùng
                generated_images = all_generated_images[-2:]
                print(f"� Displaying latest 2 images: {generated_images}")

                print(f"📄 Final data - Card info: {session.get('card_info')}")
                print(f"📄 Final data - Generated images: {generated_images}")

                return render_template('result.html',
                                     card_info=session.get('card_info', {}),
                                     generated_images=generated_images)
            else:
                print("📄 No session - showing empty result")
                return render_template('result.html',
                                     card_info={},
                                     generated_images=[],
                                     card_image=None,
                                     face_image=None)
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return render_template('result.html',
                                 card_info={},
                                 generated_images=[],
                                 card_image=None,
                                 face_image=None)

    # Lưu ý: Đã xóa route phục vụ file session - session không còn lưu file vào đĩa
    # Note: Session files route removed - sessions no longer save files to disk

    # Thêm route test camera để debug - Add test camera route for debugging
    @app.route('/test_camera')
    def test_camera():
        """Trang test camera để debug - Test camera page for debugging"""
        try:
            from flask import send_from_directory  # Import để phục vụ file tĩnh
            return send_from_directory('.', 'test_camera.html')  # Trả về file test camera
        except Exception as e:
            print(f"❌ Test camera error: {e}")  # Log lỗi test camera
            return "Test page not found", 404  # Trả về lỗi 404

    # Khởi tạo Two-Stage AI Pipeline - Initialize Two-Stage AI Pipeline
    try:
        from services.ai_pipeline_service import AIProcessingPipeline  # Import AI pipeline service
        app.ai_pipeline = AIProcessingPipeline()  # Tạo instance AI pipeline
        logger.info("Two-Stage AI Pipeline initialized successfully")  # Log thành công
        logger.info("  Stage 1: OCR Processing (Gemini 2.5 Flash)")  # Giai đoạn 1: OCR
        logger.info("  Stage 2: AI Image Generation (Gemini 2.0 Flash Preview)")  # Giai đoạn 2: Tạo ảnh AI
    except Exception as e:
        logger.warning(f"AI Pipeline initialization failed: {e}")  # Log cảnh báo nếu thất bại
        app.ai_pipeline = None  # Đặt None nếu khởi tạo thất bại

    logger.info("AI_Gen application created successfully")  # Log ứng dụng tạo thành công
    return app  # Trả về instance ứng dụng Flask


def register_blueprints(app):
    """Đăng ký tất cả blueprint của ứng dụng - Register all application blueprints"""

    # Routes chính (index, static files) - Main routes (index, static files)
    app.register_blueprint(get_main_blueprint())  # Đăng ký blueprint chính

    # Thao tác camera (video feed, capture) - Camera operations (video feed, capture)
    app.register_blueprint(get_camera_blueprint())  # Đăng ký blueprint camera

    # Thao tác xử lý (OCR, AI generation) - Processing operations (OCR, AI generation)
    app.register_blueprint(get_processing_blueprint())  # Đăng ký blueprint xử lý

    # API endpoints - Các endpoint API
    app.register_blueprint(get_api_blueprint())  # Đăng ký blueprint API

    logger.info("All blueprints registered successfully")  # Log đăng ký thành công


def setup_error_handlers(app):
    """Thiết lập các handler xử lý lỗi ứng dụng - Setup application error handlers"""

    @app.errorhandler(404)  # Decorator xử lý lỗi 404
    def not_found_error(error):
        """Xử lý lỗi 404 - trang không tìm thấy"""
        logger.warning(f"404 error: {error}")  # Log cảnh báo lỗi 404
        return "Page not found", 404  # Trả về thông báo lỗi 404

    @app.errorhandler(500)  # Decorator xử lý lỗi 500
    def internal_error(error):
        """Xử lý lỗi 500 - lỗi server nội bộ"""
        logger.error(f"500 error: {error}")  # Log lỗi 500
        return "Internal server error", 500  # Trả về thông báo lỗi 500

    @app.errorhandler(Exception)  # Decorator xử lý tất cả exception
    def handle_exception(error):
        """Xử lý tất cả exception chưa được bắt"""
        logger.error(f"Unhandled exception: {error}")  # Log exception chưa xử lý
        return f"An error occurred: {str(error)}", 500  # Trả về thông báo lỗi chung


def setup_cleanup_handlers():
    """Thiết lập các handler cleanup khi ứng dụng tắt - Setup cleanup handlers for application shutdown"""

    def cleanup():
        """Hàm cleanup được gọi khi ứng dụng thoát - Cleanup function called on application exit"""
        logger.info("Application shutting down...")  # Log ứng dụng đang tắt

        # Dọn dẹp tài nguyên camera - Cleanup camera resources
        try:
            camera_controller.cleanup()  # Gọi hàm cleanup camera
        except Exception as e:
            logger.error(f"Error during camera cleanup: {e}")  # Log lỗi cleanup camera

        # Dọn dẹp session cũ (cũ hơn 7 ngày) - Cleanup old sessions (older than 7 days)
        try:
            from models.session_model import SessionModel  # Import SessionModel
            session_model = SessionModel()  # Tạo instance SessionModel
            cleaned_count = session_model.cleanup_old_sessions(days_old=7)  # Dọn session cũ
            if cleaned_count > 0:  # Nếu có session được dọn
                logger.info(f"Cleaned up {cleaned_count} old sessions")  # Log số session đã dọn
        except Exception as e:
            logger.error(f"Error during session cleanup: {e}")  # Log lỗi cleanup session

        # Dọn dẹp file output cũ - Cleanup old output files
        try:
            from utils.helpers import cleanup_old_files  # Import hàm cleanup file
            cleaned_count = cleanup_old_files('outputs', days_old=30)  # Dọn file cũ hơn 30 ngày
            if cleaned_count > 0:  # Nếu có file được dọn
                logger.info(f"Cleaned up {cleaned_count} old output files")  # Log số file đã dọn
        except Exception as e:
            logger.error(f"Error during output cleanup: {e}")  # Log lỗi cleanup output

        logger.info("Application cleanup completed")  # Log hoàn thành cleanup

    # Đăng ký hàm cleanup - Register cleanup function
    atexit.register(cleanup)  # Đăng ký hàm cleanup với atexit


def print_startup_info():
    """In thông tin khởi động ứng dụng (tối giản như project_directory) - Print application startup information (minimal like project_directory)"""
    print("AI_Gen - Starting application...")  # In thông báo khởi động
    print("Server: http://localhost:5000")  # In địa chỉ server


# Tạo instance ứng dụng - Create application instance
app = create_app()  # Gọi factory function để tạo app

if __name__ == '__main__':
    # In thông tin khởi động - Print startup information
    print_startup_info()  # Gọi hàm in thông tin khởi động

    # Lấy cấu hình (BẮT BUỘC debug=False như project_directory) - Get configuration (FORCE debug=False like project_directory)
    debug_mode = False  # Bắt buộc tắt debug mode để hoạt động mượt mà - Force disable debug mode for smooth operation
    host = os.environ.get('FLASK_HOST', '0.0.0.0')  # Lấy host từ biến môi trường hoặc mặc định
    port = int(os.environ.get('FLASK_PORT', 5000))  # Lấy port từ biến môi trường hoặc mặc định

    logger.info(f"Starting server on {host}:{port} (debug={debug_mode})")  # Log thông tin khởi động server

    try:
        # Chạy ứng dụng - Run the application
        app.run(
            debug=debug_mode,  # Chế độ debug
            host=host,  # Địa chỉ host
            port=port,  # Cổng port
            threaded=True  # Bật chế độ đa luồng
        )
    except KeyboardInterrupt:
        logger.info("Application stopped by user")  # Log khi người dùng dừng ứng dụng
    except Exception as e:
        logger.error(f"Application startup error: {e}")  # Log lỗi khởi động
        raise  # Ném lại exception


