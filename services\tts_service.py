#!/usr/bin/env python3
"""
Text-to-Speech Service - Stage 1.5 of AI Pipeline
Xử lý chuyển đổi thông tin name card thành giọng nói
"""

import os
import time
from pathlib import Path
from typing import Dict, Optional
from io import BytesIO

# Import language detection and TTS libraries
try:
    from langdetect import detect, DetectorFactory
    from gtts import gTTS
    import pygame
    TTS_AVAILABLE = True
    print("✅ TTS libraries loaded successfully")
except ImportError as e:
    print(f"⚠️ TTS libraries not available: {e}")
    TTS_AVAILABLE = False

# Import path manager
from utils.path_manager import path_manager

class TTSService:
    """
    Text-to-Speech Service - Stage 1.5 của AI Pipeline
    
    Features:
    - Auto language detection (Vietnamese/English)
    - Generate speech from business card info
    - Save audio files with proper naming
    - Play audio during result display
    """
    
    def __init__(self):
        """Initialize TTS Service"""
        print("🔊 Initializing TTS Service (Stage 1.5)...")
        
        if not TTS_AVAILABLE:
            print("❌ TTS Service disabled - missing dependencies")
            print("   Install with: pip install langdetect gTTS pygame")
            self.enabled = False
            return
        
        try:
            # Set language detection seed for consistency
            DetectorFactory.seed = 0
            
            # Initialize pygame mixer for audio playback
            pygame.mixer.init()
            self.audio_enabled = True
            print("✅ Audio playback initialized")
            
        except Exception as e:
            print(f"⚠️ Audio playback disabled: {e}")
            self.audio_enabled = False
        
        self.enabled = True
        self.supported_languages = {
            'vi': 'Vietnamese',
            'en': 'English',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean'
        }
        
        print("✅ TTS Service initialized successfully")
        print(f"   Supported languages: {list(self.supported_languages.keys())}")
    
    def generate_speech_from_card_info(self, card_info: Dict) -> Dict:
        """Generate speech from business card information"""
        
        print(f"\n🔊 STAGE 1.5: Text-to-Speech Processing")
        print(f"   TTS Enabled: {self.enabled}")
        print(f"   TTS Available: {TTS_AVAILABLE}")
        
        if not self.enabled:
            return {
                'success': False,
                'error': 'TTS Service not available - missing dependencies (langdetect, gTTS, pygame)'
            }
        
        tts_start_time = time.time()
        
        try:
            # Create speech text from card info
            speech_text = self._create_speech_text(card_info)
            if not speech_text:
                raise ValueError("No text to convert to speech")
            
            print(f"   📝 Speech text: {speech_text[:100]}...")
            
            # Detect language
            detected_lang = self._detect_language(speech_text)
            print(f"   🌐 Detected language: {self.supported_languages.get(detected_lang, detected_lang)}")
            
            # Generate speech audio
            audio_path = self._generate_audio(speech_text, detected_lang, card_info)
            if not audio_path:
                raise ValueError("Failed to generate audio")
            
            tts_end_time = time.time()
            processing_time = f"{tts_end_time - tts_start_time:.2f}s"
            
            result = {
                'success': True,
                'stage': 'Text-to-Speech',
                'processing_time': processing_time,
                'data': {
                    'audio_path': str(audio_path),
                    'speech_text': speech_text,
                    'detected_language': detected_lang,
                    'language_name': self.supported_languages.get(detected_lang, 'Unknown')
                },
                'metadata': {
                    'text_length': len(speech_text),
                    'audio_format': 'mp3',
                    'can_play': self.audio_enabled
                }
            }
            
            print(f"   ✅ Stage 1.5 Completed: {processing_time}")
            print(f"   🎵 Audio saved: {Path(audio_path).name}")
            
            return result
            
        except Exception as e:
            tts_end_time = time.time()
            processing_time = f"{tts_end_time - tts_start_time:.2f}s"
            
            print(f"   ❌ Stage 1.5 Failed: {e} ({processing_time})")
            
            return {
                'success': False,
                'stage': 'Text-to-Speech',
                'processing_time': processing_time,
                'error': str(e)
            }
    
    def _create_speech_text(self, card_info: Dict) -> str:
        """Create natural speech text from card information"""
        
        name = card_info.get('name', '').strip()
        title = card_info.get('title', '').strip()
        company = card_info.get('company', '').strip()
        email = card_info.get('email', '').strip()
        phone = card_info.get('phone', '').strip()
        
        # Detect if Vietnamese or English based on content
        has_vietnamese = any(ord(char) > 127 for char in f"{name} {title} {company}")
        
        if has_vietnamese:
            # Vietnamese speech template
            speech_parts = []
            
            if name:
                speech_parts.append(f"Welcome, ngài {name}")          
        else:
            # English speech template
            speech_parts = []
            
            if name:
                speech_parts.append(f"Welcome, Mr. {name}")
            
            speech_parts.append("Nice to meet you!")
        
        return ". ".join(speech_parts)
    
    def _detect_language(self, text: str) -> str:
        """Detect language of the text"""
        try:
            detected = detect(text)
            
            # Map to supported gTTS languages
            if detected in self.supported_languages:
                return detected
            elif detected in ['zh-cn', 'zh-tw']:
                return 'zh'
            else:
                # Default to Vietnamese if contains Vietnamese characters
                if any(ord(char) > 127 for char in text):
                    return 'vi'
                else:
                    return 'en'
                    
        except Exception as e:
            print(f"   ⚠️ Language detection failed: {e}")
            # Fallback: check for Vietnamese characters
            if any(ord(char) > 127 for char in text):
                return 'vi'
            else:
                return 'en'
    
    def _generate_audio(self, text: str, language: str, card_info: Dict) -> Optional[Path]:
        """Generate audio file from text"""
        try:
            # Create gTTS object
            tts = gTTS(text=text, lang=language, slow=False)
            
            # Generate audio file path
            person_name = card_info.get('name', 'person')
            audio_path = path_manager.get_audio_file_path(person_name, language)
            
            # Ensure directory exists
            audio_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save audio file
            tts.save(str(audio_path))
            
            print(f"   💾 Audio saved: {audio_path}")
            return audio_path
            
        except Exception as e:
            print(f"   ❌ Audio generation failed: {e}")
            return None
    
    def play_audio(self, audio_path: str) -> bool:
        """Play audio file"""
        if not self.audio_enabled:
            print("   ⚠️ Audio playback not available")
            return False
        
        try:
            print(f"   🔊 Playing audio: {Path(audio_path).name}")
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.play()
            
            return True
            
        except Exception as e:
            print(f"   ❌ Audio playback failed: {e}")
            return False
    
    def stop_audio(self):
        """Stop audio playback"""
        if self.audio_enabled:
            try:
                pygame.mixer.music.stop()
                print("   ⏹️ Audio stopped")
            except:
                pass

def test_tts_service():
    """Test TTS Service"""
    print("🧪 Testing TTS Service")
    print("=" * 50)
    
    tts = TTSService()
    
    if not tts.enabled:
        print("❌ TTS Service not available for testing")
        return
    
    # Test card info
    test_card_info = {
        'name': 'Nguyễn Văn An',
        'title': 'Software Engineer',
        'company': 'Tech Company Ltd',
        'email': '<EMAIL>',
        'phone': '0123456789'
    }
    
    print(f"\n🎤 Testing TTS with sample card info:")
    for key, value in test_card_info.items():
        print(f"   {key}: {value}")
    
    result = tts.generate_speech_from_card_info(test_card_info)
    
    if result['success']:
        print(f"\n✅ TTS Test Successful!")
        print(f"   Processing Time: {result['processing_time']}")
        print(f"   Language: {result['data']['language_name']}")
        print(f"   Audio File: {result['data']['audio_path']}")
        print(f"   Speech Text: {result['data']['speech_text']}")
        
        # Test playback
        if tts.audio_enabled:
            print(f"\n🔊 Testing audio playback...")
            tts.play_audio(result['data']['audio_path'])
            time.sleep(2)  # Let it play for 2 seconds
            tts.stop_audio()
    else:
        print(f"\n❌ TTS Test Failed: {result['error']}")

if __name__ == "__main__":
    test_tts_service()

