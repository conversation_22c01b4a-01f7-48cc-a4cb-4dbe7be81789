@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset & Variables */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-card: rgba(255, 255, 255, 0.08);
    --bg-glass: rgba(255, 255, 255, 0.05);
    
    /* Accent Colors */
    --blue-primary: #3b82f6;
    --blue-secondary: #1d4ed8;
    --purple-primary: #8b5cf6;
    --green-success: #10b981;
    --orange-warning: #f59e0b;
    --red-error: #ef4444;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* Compact Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-xxl: 2rem;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* App Container */
.app-container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header - Compact */
.app-header {
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(26, 26, 46, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--space-md) var(--space-lg);
    position: relative;
    overflow: hidden;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-primary), var(--purple-primary), var(--blue-primary));
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo-icon {
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--blue-primary), var(--purple-primary));
    border-radius: var(--radius-md);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    position: relative;
}

.logo-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, var(--blue-primary), var(--purple-primary), var(--blue-primary));
    border-radius: var(--radius-md);
    z-index: -1;
    opacity: 0.3;
}

.logo-text h1 {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--blue-primary), var(--purple-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

.header-subtitle p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Main Content - Compact */
.main-content {
    flex: 1;
    padding: var(--space-lg) var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* Camera Section - Compact */
.camera-section-dominant {
    margin-bottom: var(--space-md);
    position: relative;
}

.camera-section-dominant::before {
    content: '';
    position: absolute;
    top: -var(--space-sm);
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.camera-grid-large {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    height: 55vh;
    min-height: 350px;
    margin-bottom: var(--space-lg);
}

.camera-card-large {
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(26, 26, 46, 0.8) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.camera-card-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-primary), var(--purple-primary));
    opacity: 0.6;
}

.camera-card-large:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(96, 165, 250, 0.3);
}

.camera-card-large:hover::before {
    opacity: 1;
}

.camera-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-md);
}

.camera-info {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.camera-icon {
    font-size: 1.8rem;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.camera-details h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.camera-details p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.camera-status {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.status-ready {
    background: var(--text-muted);
}

.status-dot.status-captured {
    background: var(--green-success);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-dot.status-processing {
    background: var(--orange-warning);
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.3); opacity: 0.7; }
}

.status-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Camera Display Large */
.camera-display-large {
    position: relative;
}

.camera-frame-large {
    position: relative;
    width: 100% !important;
    height: 500px !important;  /* Force 500px height */
    min-height: 500px !important;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--bg-primary);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.camera-frame-large img {
    width: 100% !important;
    height: 100% !important;
    min-height: 500px !important;
    object-fit: cover;
    display: block;
}

/* Focus Guides */
.focus-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.guide-corner {
    position: absolute;
    width: 25px;
    height: 25px;
    border: 2px solid var(--blue-primary);
    opacity: 0.7;
}

.guide-corner.tl {
    top: 20px;
    left: 20px;
    border-right: none;
    border-bottom: none;
}

.guide-corner.tr {
    top: 20px;
    right: 20px;
    border-left: none;
    border-bottom: none;
}

.guide-corner.bl {
    bottom: 20px;
    left: 20px;
    border-right: none;
    border-top: none;
}

.guide-corner.br {
    bottom: 20px;
    right: 20px;
    border-left: none;
    border-top: none;
}

.center-target {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.target-h, .target-v {
    position: absolute;
    background: var(--blue-primary);
    opacity: 0.6;
}

.target-h {
    width: 20px;
    height: 1px;
    top: 0;
    left: -10px;
}

.target-v {
    width: 1px;
    height: 20px;
    top: -10px;
    left: 0;
}

/* Face Guides */
.face-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-oval {
    width: 160px;
    height: 200px;
    border: 2px solid var(--purple-primary);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.7;
    position: relative;
}

.oval-inner {
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    border: 1px solid var(--purple-primary);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.5;
    animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.02); opacity: 0.8; }
}

/* Camera Status Overlay */
.camera-status-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    padding: var(--space-sm);
    text-align: center;
    z-index: 4;
}

.status-message {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Scan Overlay */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.3) 50%,
        transparent 100%
    );
    animation: scanAnimation 2s ease-in-out infinite;
    z-index: 5;
}

@keyframes scanAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Capture Section */
.capture-section {
    margin-bottom: var(--space-xl);
}

.capture-container {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.instruction-display {
    margin-bottom: var(--space-lg);
}

.instruction-message {
    color: var(--text-secondary);
    font-size: 1.1rem;
    padding: var(--space-md);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-md);
}

.capture-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.capture-button {
    padding: var(--space-lg) var(--space-xxl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    min-width: 180px;
    justify-content: center;
    text-decoration: none;
}

.capture-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.capture-button.primary {
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    color: white;
    box-shadow: var(--shadow-md);
}

.capture-button.primary:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.capture-button.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.capture-button.secondary:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-icon {
    font-size: 1.3rem;
}

.btn-text {
    font-weight: 600;
}

/* AI Generation Section - Compact */
.ai-generation-section {
    margin-bottom: var(--space-lg);
    position: relative;
}

.ai-generation-section::before {
    content: '';
    position: absolute;
    top: -var(--space-sm);
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.ai-container {
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(16, 16, 32, 0.9) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.ai-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(96, 165, 250, 0.05) 50%, transparent 70%);
    pointer-events: none;
}

.ai-config-area {
    margin-bottom: var(--space-lg);
}

.config-row {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-sm);
    justify-content: center;
}

.config-label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 100px;
}

.style-select {
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    appearance: none;
    transition: all 0.3s ease;
    min-width: 200px;
}

.style-select:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.style-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-style: italic;
    text-align: center;
}

.ai-actions {
    margin-bottom: var(--space-lg);
}

.ai-button {
    padding: var(--space-lg) var(--space-xxl);
    background: linear-gradient(135deg, var(--purple-primary), var(--blue-primary));
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    min-width: 200px;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.ai-button:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.ai-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Progress Section */
.progress-section {
    margin-top: var(--space-lg);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--purple-primary), var(--blue-primary));
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: 0 0 4px rgba(139, 92, 246, 0.5); }
    50% { box-shadow: 0 0 12px rgba(139, 92, 246, 0.8); }
}

.progress-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Status Messages */
.status-container {
    text-align: center;
    margin-top: var(--space-lg);
}

.status-message {
    padding: var(--space-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.status-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--green-success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--red-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-message.processing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--orange-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-grid-large {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .main-content {
        padding: var(--space-md) var(--space-sm);
        gap: var(--space-md);
    }

    .camera-grid-large {
        height: 45vh;
        min-height: 300px;
        gap: var(--space-md);
    }

    .camera-card-large {
        padding: var(--space-sm);
    }

    .ai-container {
        padding: var(--space-md);
        max-width: 100%;
    }

    .camera-frame-large {
        height: 400px;  /* Increased mobile height too */
    }

    .capture-actions {
        flex-direction: column;
        align-items: center;
    }

    .capture-button, .ai-button {
        width: 100%;
        max-width: 250px;
    }

    .config-row {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .config-label {
        min-width: auto;
    }

    .style-select {
        width: 100%;
        max-width: 300px;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .logo-text h1 {
        font-size: 1.5rem;
    }
}

/* Camera Main Container Box */
.camera-main-box {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    margin: 20px auto;
    max-width: 1500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Center camera grid */
.apple-camera-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    justify-content: center;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

/* Ensure camera containers maintain size */
.camera-container {
    min-width: 600px;
    max-width: 700px;
}

.camera-frame {
    min-height: 450px;
    max-height: 500px;
}

.camera-stream {
    width: 100%;
    height: 100%;
    object-fit: cover;
    min-height: 450px;
}



