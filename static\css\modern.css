@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --accent-bg: #16213e;
    --card-bg: rgba(26, 26, 46, 0.8);
    --glass-bg: rgba(255, 255, 255, 0.05);
    
    /* Accent Colors */
    --primary-blue: #4facfe;
    --secondary-blue: #00f2fe;
    --accent-purple: #8a2be2;
    --success-green: #00ff88;
    --warning-orange: #ff6b35;
    --error-red: #ff4757;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8a8aa0;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(79, 172, 254, 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: 
        radial-gradient(circle at 20% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: 
        linear-gradient(135deg, var(--card-bg) 0%, rgba(22, 33, 62, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(79, 172, 254, 0.2);
    padding: var(--spacing-xl) var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
    animation: headerShimmer 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes headerShimmer {
    0%, 100% { transform: translateX(-100%); opacity: 0; }
    50% { transform: translateX(100%); opacity: 1; }
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-lg);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-glow);
}

.logo-text h1 {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.header-subtitle {
    text-align: right;
}

.header-subtitle p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

.header-decoration {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-purple));
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-xl) var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Camera Section */
.camera-section {
    margin-bottom: var(--spacing-xl);
}

.camera-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.camera-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(79, 172, 254, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.camera-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.camera-card:hover {
    transform: translateY(-8px);
    border-color: rgba(79, 172, 254, 0.4);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.camera-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 2;
}

.camera-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.camera-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.camera-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.camera-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.status-ready {
    background: var(--text-muted);
    box-shadow: 0 0 8px rgba(138, 138, 160, 0.3);
}

.status-dot.status-captured {
    background: var(--success-green);
    box-shadow: 0 0 12px rgba(0, 255, 136, 0.5);
}

.status-dot.status-processing {
    background: var(--warning-orange);
    box-shadow: 0 0 12px rgba(255, 107, 53, 0.5);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

.status-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Camera Viewport */
.camera-viewport {
    position: relative;
    z-index: 2;
}

.camera-frame {
    position: relative;
    width: 100%;
    height: 300px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--primary-bg);
    border: 2px solid rgba(79, 172, 254, 0.3);
}

.camera-frame img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Focus Guide */
.focus-guide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.guide-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-blue);
    opacity: 0.8;
}

.guide-corner.tl {
    top: 10px;
    left: 10px;
    border-right: none;
    border-bottom: none;
}

.guide-corner.tr {
    top: 10px;
    right: 10px;
    border-left: none;
    border-bottom: none;
}

.guide-corner.bl {
    bottom: 10px;
    left: 10px;
    border-right: none;
    border-top: none;
}

.guide-corner.br {
    bottom: 10px;
    right: 10px;
    border-left: none;
    border-top: none;
}

/* Detection Overlay */
.detection-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: var(--spacing-sm);
    z-index: 4;
}

.detection-text {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    display: block;
}

/* Scan Overlay */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(79, 172, 254, 0.3) 50%,
        transparent 100%
    );
    animation: scanAnimation 2s ease-in-out infinite;
    z-index: 5;
}

@keyframes scanAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Control Section */
.control-section {
    margin-bottom: var(--spacing-xl);
}

.control-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(79, 172, 254, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.control-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Step Indicator */
.step-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.step-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.progress-line {
    width: 60px;
    height: 2px;
    background: var(--text-muted);
    border-radius: 1px;
}

.step-dot {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--text-muted);
    color: var(--primary-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.step-dot.active {
    background: var(--primary-blue);
    box-shadow: 0 0 12px rgba(79, 172, 254, 0.5);
}

.step-dot.completed {
    background: var(--success-green);
    box-shadow: 0 0 12px rgba(0, 255, 136, 0.5);
}

/* Instructions */
.capture-instructions {
    margin-bottom: var(--spacing-lg);
}

.instruction-text {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.action-btn {
    position: relative;
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    box-shadow: var(--shadow-md);
}

.action-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.action-btn.secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid rgba(79, 172, 254, 0.3);
}

.action-btn.secondary:hover:not(:disabled) {
    background: rgba(79, 172, 254, 0.1);
    border-color: rgba(79, 172, 254, 0.5);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-weight: 600;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.action-btn.primary:hover .btn-glow {
    transform: translateX(100%);
}

/* AI Section */
.ai-section {
    margin-bottom: var(--spacing-xl);
}

.ai-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.ai-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.ai-icon {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--accent-purple), var(--primary-blue));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.ai-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.ai-info p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Style Selection */
.style-selection {
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.style-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    font-weight: 600;
}

.label-icon {
    font-size: 1.3rem;
}

.select-wrapper {
    position: relative;
    margin-bottom: var(--spacing-sm);
}

.style-select {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--glass-bg);
    border: 1px solid rgba(79, 172, 254, 0.3);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    appearance: none;
    transition: all 0.3s ease;
}

.style-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
}

.select-arrow {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    font-size: 0.8rem;
}

.style-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-style: italic;
    text-align: center;
}

/* Generate Controls */
.generate-controls {
    text-align: center;
    position: relative;
    z-index: 2;
}

.generate-btn {
    position: relative;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, var(--accent-purple), var(--primary-blue));
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 200px;
    justify-content: center;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg), 0 0 30px rgba(138, 43, 226, 0.4);
}

.generate-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.3) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: particles 3s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.generate-btn:hover .btn-particles {
    opacity: 1;
}

@keyframes particles {
    0% { transform: translateY(0); }
    100% { transform: translateY(-20px); }
}

/* Progress Container */
.progress-container {
    margin-top: var(--spacing-lg);
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(79, 172, 254, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(79, 172, 254, 0.5); }
    50% { box-shadow: 0 0 15px rgba(79, 172, 254, 0.8); }
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Status Messages */
.status-container {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.status-message {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.status-message.success {
    background: rgba(0, 255, 136, 0.1);
    color: var(--success-green);
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-message.error {
    background: rgba(255, 71, 87, 0.1);
    color: var(--error-red);
    border: 1px solid rgba(255, 71, 87, 0.3);
}

.status-message.processing {
    background: rgba(255, 107, 53, 0.1);
    color: var(--warning-orange);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .camera-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .header-subtitle {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .app-header {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .main-content {
        padding: var(--spacing-lg) var(--spacing-md);
        gap: var(--spacing-lg);
    }

    .logo-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .logo-text h1 {
        font-size: 2rem;
    }

    .camera-frame {
        height: 250px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 100%;
        max-width: 300px;
    }
}
