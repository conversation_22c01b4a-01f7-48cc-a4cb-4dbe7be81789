@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset & Variables */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette */
    --bg-primary: #0f1419;
    --bg-secondary: #1a1f2e;
    --bg-card: rgba(255, 255, 255, 0.08);
    --bg-glass: rgba(255, 255, 255, 0.05);
    
    /* Accent Colors */
    --blue-primary: #3b82f6;
    --blue-secondary: #1d4ed8;
    --purple-primary: #8b5cf6;
    --green-success: #10b981;
    --orange-warning: #f59e0b;
    --red-error: #ef4444;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* App Container */
.app-container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--space-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo-icon {
    font-size: 2rem;
    background: linear-gradient(135deg, var(--blue-primary), var(--purple-primary));
    border-radius: var(--radius-md);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.logo-text h1 {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--blue-primary), var(--purple-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

.header-subtitle p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--space-xl) var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

/* Section Titles */
.section-title {
    text-align: center;
    margin-bottom: var(--space-lg);
}

.section-title h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: var(--space-xs);
    color: var(--text-primary);
}

.section-title p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Camera Section */
.camera-section {
    margin-bottom: var(--space-xl);
}

.camera-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    max-width: 1000px;
    margin: 0 auto;
}

.camera-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    transition: all 0.3s ease;
}

.camera-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(255, 255, 255, 0.2);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-md);
}

.camera-label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.label-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.label-text h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.label-text p {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.status-ready {
    background: var(--text-muted);
}

.status-dot.status-captured {
    background: var(--green-success);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-dot.status-processing {
    background: var(--orange-warning);
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.3); opacity: 0.7; }
}

.status-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Camera Viewport */
.camera-viewport {
    position: relative;
}

.camera-frame {
    position: relative;
    width: 100%;
    height: 280px;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--bg-primary);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.camera-frame img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Focus Guides */
.focus-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.guide-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid var(--blue-primary);
    opacity: 0.7;
}

.guide-corner.tl {
    top: 15px;
    left: 15px;
    border-right: none;
    border-bottom: none;
}

.guide-corner.tr {
    top: 15px;
    right: 15px;
    border-left: none;
    border-bottom: none;
}

.guide-corner.bl {
    bottom: 15px;
    left: 15px;
    border-right: none;
    border-top: none;
}

.guide-corner.br {
    bottom: 15px;
    right: 15px;
    border-left: none;
    border-top: none;
}

.center-target {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.target-h, .target-v {
    position: absolute;
    background: var(--blue-primary);
    opacity: 0.6;
}

.target-h {
    width: 16px;
    height: 1px;
    top: 0;
    left: -8px;
}

.target-v {
    width: 1px;
    height: 16px;
    top: -8px;
    left: 0;
}

/* Face Guides */
.face-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-oval {
    width: 140px;
    height: 180px;
    border: 2px solid var(--purple-primary);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.7;
    position: relative;
}

.oval-inner {
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    border: 1px solid var(--purple-primary);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.5;
    animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.02); opacity: 0.8; }
}

/* Camera Status */
.camera-status {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    padding: var(--space-sm);
    text-align: center;
    z-index: 4;
}

.status-message {
    color: var(--text-primary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Scan Overlay */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.3) 50%,
        transparent 100%
    );
    animation: scanAnimation 2s ease-in-out infinite;
    z-index: 5;
}

@keyframes scanAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Action Section */
.action-section {
    margin-top: var(--space-xl);
}

.action-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    max-width: 1000px;
    margin: 0 auto;
}

.capture-panel, .ai-panel {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
}

.panel-header {
    margin-bottom: var(--space-lg);
}

.panel-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Instruction Area */
.instruction-area {
    margin-bottom: var(--space-lg);
}

.instruction-text {
    color: var(--text-secondary);
    font-size: 1rem;
    padding: var(--space-md);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-md);
}

/* Button Groups */
.button-group {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.action-button {
    padding: var(--space-md) var(--space-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    min-width: 140px;
    justify-content: center;
    text-decoration: none;
}

.action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.action-button.primary {
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    color: white;
    box-shadow: var(--shadow-md);
}

.action-button.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-button.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-button.secondary:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.action-button.ai-generate {
    background: linear-gradient(135deg, var(--purple-primary), var(--blue-primary));
    color: white;
    box-shadow: var(--shadow-md);
}

.action-button.ai-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-icon {
    font-size: 1.1rem;
}

.btn-text {
    font-weight: 600;
}

/* AI Configuration */
.ai-config {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.config-group {
    text-align: left;
}

.config-label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

.config-select {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.95rem;
    cursor: pointer;
    appearance: none;
    transition: all 0.3s ease;
}

.config-select:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.config-description {
    color: var(--text-muted);
    font-size: 0.85rem;
    font-style: italic;
    margin-top: var(--space-xs);
}

/* Progress Container */
.progress-container {
    margin-top: var(--space-lg);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--purple-primary), var(--blue-primary));
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: 0 0 4px rgba(139, 92, 246, 0.5); }
    50% { box-shadow: 0 0 12px rgba(139, 92, 246, 0.8); }
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

/* Status Messages */
.status-container {
    text-align: center;
    margin-top: var(--space-lg);
}

.status-message {
    padding: var(--space-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.status-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--green-success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--red-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-message.processing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--orange-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .action-container {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .main-content {
        padding: var(--space-lg) var(--space-md);
        gap: var(--space-lg);
    }

    .camera-frame {
        height: 240px;
    }

    .button-group {
        flex-direction: column;
        align-items: center;
    }

    .action-button {
        width: 100%;
        max-width: 200px;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .logo-text h1 {
        font-size: 1.5rem;
    }
}
