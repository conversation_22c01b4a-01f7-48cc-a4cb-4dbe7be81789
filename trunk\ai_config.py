"""
AI Configuration - Simple prompt loader
Chỉ đọc prompt từ file prompts/prompt.txt
"""

from pathlib import Path  # Import thư viện để làm việc với đường dẫn file

def get_available_prompts():
    """
    <PERSON><PERSON><PERSON> danh sách tất cả prompt files có sẵn với tên hiển thị tiếng Việt
    """
    prompts_dir = Path("prompts")  # Đường dẫn đến thư mục prompts
    if not prompts_dir.exists():  # Kiểm tra thư mục có tồn tại không
        return {}  # Trả về dict rỗng nếu không tồn tại

    # Mapping tên file với tên hiển thị tiếng Việt
    display_names = {
        'prompt': 'Dollhouse (Mặc định)',
        'dollhouse': 'Dollhouse Miniature',
        'professional': 'Chuyên nghiệp',
        'artistic': '<PERSON><PERSON><PERSON> thuật',
        'cartoon': '<PERSON><PERSON><PERSON> hình',
        'cinematic': 'Điện ảnh',
        'luxury': 'Sang trọng'
    }

    available_prompts = {}  # Dictionary lưu thông tin các prompt có sẵn
    for prompt_file in prompts_dir.glob("*.txt"):  # Lặp qua tất cả file .txt trong thư mục prompts
        prompt_name = prompt_file.stem  # Lấy tên file không bao gồm phần mở rộng
        # Lấy tên hiển thị từ mapping hoặc tạo tên hiển thị từ tên file
        display_name = display_names.get(prompt_name, prompt_name.replace('_', ' ').title())

        # Thêm thông tin prompt vào dictionary
        available_prompts[prompt_name] = {
            'file': str(prompt_file),  # Đường dẫn đến file prompt
            'name': display_name,  # Tên hiển thị
            'description': get_prompt_description(prompt_name)  # Mô tả prompt
        }

    return available_prompts

def get_prompt_description(prompt_name):
    """
    Trả về mô tả ngắn cho từng prompt template
    """
    descriptions = {
        'prompt': 'Style dollhouse miniature cổ điển với môi trường văn phòng thu nhỏ',
        'dollhouse': 'Tạo cảnh dollhouse miniature chuyên nghiệp với chi tiết tinh xảo',
        'professional': 'Ảnh chân dung doanh nhân chuyên nghiệp trong môi trường công sở',
        'artistic': 'Phong cách nghệ thuật sáng tạo với hiệu ứng ánh sáng độc đáo',
        'cartoon': 'Phong cách hoạt hình vui nhộn và thân thiện',
        'cinematic': 'Phong cách điện ảnh với ánh sáng và góc quay chuyên nghiệp',
        'luxury': 'Phong cách sang trọng và đẳng cấp cao'
    }
    return descriptions.get(prompt_name, 'Prompt template tùy chỉnh')

def load_prompt(prompt_name="prompt"):
    """
    Đọc prompt từ file prompts/{prompt_name}.txt
    Trả về prompt thuần túy, không thêm gì cả

    Args:
        prompt_name (str): Tên file prompt (không cần .txt extension)
    """
    prompt_file = Path(f"prompts/{prompt_name}.txt")  # Tạo đường dẫn đến file prompt

    if not prompt_file.exists():  # Kiểm tra file prompt có tồn tại không
        # Fallback to default prompt.txt - Sử dụng file prompt mặc định nếu không tìm thấy
        prompt_file = Path("prompts/prompt.txt")
        if not prompt_file.exists():  # Kiểm tra file mặc định có tồn tại không
            raise FileNotFoundError(f"Prompt file not found: {prompt_file}")  # Báo lỗi nếu không tìm thấy

    try:
        # Mở và đọc nội dung file prompt với encoding UTF-8
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt = f.read().strip()  # Đọc và loại bỏ khoảng trắng thừa

        if not prompt:  # Kiểm tra prompt có nội dung không
            raise ValueError("Prompt file is empty")  # Báo lỗi nếu file rỗng

        # In thông báo đã load prompt thành công
        print(f"✅ Loaded prompt from {prompt_file}: {len(prompt)} characters")
        return prompt  # Trả về nội dung prompt

    except Exception as e:  # Bắt các lỗi khác khi đọc file
        raise Exception(f"Error reading prompt file: {str(e)}")  # Báo lỗi với thông tin chi tiết

def get_gemini_config():
    """
    Trả về config cơ bản cho Gemini 2.0
    """
    return {
        'model': 'gemini-2.0-flash-preview-image-generation',  # Model tạo ảnh
        'fallback_model': 'gemini-2.5-flash'  # Model dự phòng nếu model chính không khả dụng
    }
