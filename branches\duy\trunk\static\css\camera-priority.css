@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Reset & Variables */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-card: rgba(26, 26, 46, 0.9);
    --bg-glass: rgba(255, 255, 255, 0.05);
    
    --blue-primary: #4facfe;
    --blue-secondary: #00f2fe;
    --purple-accent: #8a2be2;
    --green-success: #00ff88;
    --orange-warning: #ff6b35;
    --red-error: #ff4757;
    
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8a8aa0;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    
    /* Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
}

body {
    font-family: 'Inter', sans-serif;
    background: 
        radial-gradient(circle at 20% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* App Container */
.app-container {
    max-width: 1600px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(79, 172, 254, 0.2);
    padding: var(--space-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    border-radius: var(--radius-lg);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
}

.logo-text h1 {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.header-subtitle p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--space-xl) var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

/* Camera Section - Priority Layout */
.camera-section-priority {
    margin-bottom: var(--space-xl);
}

.camera-container-large {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* Primary Camera (Business Card) */
.camera-primary {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(79, 172, 254, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    position: relative;
    overflow: hidden;
}

.camera-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(79, 172, 254, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.camera-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-md);
    position: relative;
    z-index: 2;
}

.title-icon {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.title-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.title-content p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.camera-status-badge {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.status-ready {
    background: var(--text-muted);
    box-shadow: 0 0 8px rgba(138, 138, 160, 0.3);
}

.status-dot.status-captured {
    background: var(--green-success);
    box-shadow: 0 0 12px rgba(0, 255, 136, 0.5);
}

.status-dot.status-processing {
    background: var(--orange-warning);
    box-shadow: 0 0 12px rgba(255, 107, 53, 0.5);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

.status-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Camera Display */
.camera-display {
    position: relative;
    z-index: 2;
}

.camera-frame-large {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-primary);
    border: 2px solid rgba(79, 172, 254, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.camera-frame-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Focus Grid for Business Card */
.focus-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.grid-line {
    position: absolute;
    background: rgba(79, 172, 254, 0.6);
    opacity: 0.7;
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
}

.grid-line.horizontal.top { top: 33.33%; }
.grid-line.horizontal.bottom { bottom: 33.33%; }

.grid-line.vertical {
    height: 100%;
    width: 1px;
}

.grid-line.vertical.left { left: 33.33%; }
.grid-line.vertical.right { right: 33.33%; }

.center-cross {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.cross-h, .cross-v {
    position: absolute;
    background: rgba(79, 172, 254, 0.8);
}

.cross-h {
    width: 20px;
    height: 1px;
    top: 0;
    left: -10px;
}

.cross-v {
    width: 1px;
    height: 20px;
    top: -10px;
    left: 0;
}

/* Corner Guides */
.corner-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 4;
}

.corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--blue-primary);
    opacity: 0.8;
}

.corner.tl {
    top: 20px;
    left: 20px;
    border-right: none;
    border-bottom: none;
}

.corner.tr {
    top: 20px;
    right: 20px;
    border-left: none;
    border-bottom: none;
}

.corner.bl {
    bottom: 20px;
    left: 20px;
    border-right: none;
    border-top: none;
}

.corner.br {
    bottom: 20px;
    right: 20px;
    border-left: none;
    border-top: none;
}

/* Secondary Camera (Face) */
.camera-secondary {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--space-md);
    position: relative;
    overflow: hidden;
}

.camera-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.camera-secondary .title-icon {
    width: 50px;
    height: 50px;
    font-size: 1.8rem;
    background: linear-gradient(135deg, var(--purple-accent), var(--blue-primary));
}

.camera-secondary .title-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.camera-frame-medium {
    position: relative;
    width: 100%;
    height: 280px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-primary);
    border: 2px solid rgba(138, 43, 226, 0.4);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.camera-frame-medium img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Face Guide */
.face-guide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.face-circle {
    width: 180px;
    height: 220px;
    border: 2px solid rgba(138, 43, 226, 0.6);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
}

.face-outline {
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: faceGlow 2s ease-in-out infinite;
}

@keyframes faceGlow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.02); }
}

/* Status Overlay */
.status-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: var(--space-sm);
    z-index: 5;
}

.status-content {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    justify-content: center;
}

.status-icon {
    font-size: 1.2rem;
}

.status-message {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Scan Overlay */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(79, 172, 254, 0.3) 50%,
        transparent 100%
    );
    animation: scanAnimation 2s ease-in-out infinite;
    z-index: 6;
}

@keyframes scanAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Control Panel */
.control-panel {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(79, 172, 254, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    max-width: 1400px;
    margin: 0 auto;
}

.control-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xl);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.title-icon {
    font-size: 1.5rem;
}

.section-title h3 {
    font-size: 1.4rem;
    font-weight: 600;
}

/* Capture Section */
.capture-section {
    text-align: center;
}

.instruction-message {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: rgba(79, 172, 254, 0.1);
    border-radius: var(--radius-md);
    border: 1px solid rgba(79, 172, 254, 0.2);
}

.capture-buttons {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
}

.capture-btn {
    padding: var(--space-md) var(--space-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    min-width: 150px;
    justify-content: center;
}

.capture-btn.primary {
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-secondary));
    color: white;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.capture-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
}

.capture-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(79, 172, 254, 0.3);
}

.capture-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* AI Section */
.ai-section {
    text-align: center;
}

.ai-controls {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.style-selector {
    text-align: left;
}

.selector-label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

.select-container {
    margin-bottom: var(--space-sm);
}

.style-dropdown {
    width: 100%;
    padding: var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    appearance: none;
    transition: all 0.3s ease;
}

.style-dropdown:focus {
    outline: none;
    border-color: var(--purple-accent);
    box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.2);
}

.style-info {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-style: italic;
}

.generate-btn {
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--purple-accent), var(--blue-primary));
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    justify-content: center;
    min-width: 200px;
    margin: 0 auto;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 32px rgba(138, 43, 226, 0.4);
}

.generate-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Progress Section */
.progress-section {
    margin-top: var(--space-lg);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(138, 43, 226, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--purple-accent), var(--blue-primary));
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(138, 43, 226, 0.5); }
    50% { box-shadow: 0 0 15px rgba(138, 43, 226, 0.8); }
}

.progress-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Status Messages */
.status-container {
    text-align: center;
    margin-top: var(--space-lg);
}

.status-message {
    padding: var(--space-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.status-message.success {
    background: rgba(0, 255, 136, 0.1);
    color: var(--green-success);
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-message.error {
    background: rgba(255, 71, 87, 0.1);
    color: var(--red-error);
    border: 1px solid rgba(255, 71, 87, 0.3);
}

.status-message.processing {
    background: rgba(255, 107, 53, 0.1);
    color: var(--orange-warning);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .camera-container-large {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .camera-frame-large {
        height: 350px;
    }

    .camera-frame-medium {
        height: 250px;
    }

    .control-container {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .app-header {
        padding: var(--space-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .main-content {
        padding: var(--space-lg) var(--space-md);
        gap: var(--space-lg);
    }

    .logo-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .logo-text h1 {
        font-size: 1.8rem;
    }

    .camera-frame-large {
        height: 280px;
    }

    .camera-frame-medium {
        height: 200px;
    }

    .capture-buttons {
        flex-direction: column;
        align-items: center;
    }

    .capture-btn {
        width: 100%;
        max-width: 250px;
    }

    .control-panel {
        padding: var(--space-lg);
    }

    .title-content h2 {
        font-size: 1.5rem;
    }

    .camera-secondary .title-content h3 {
        font-size: 1.2rem;
    }
}
