{% extends "base.html" %}

{% block title %}AI Generator - Tạo Ảnh <PERSON> <PERSON>{% endblock %}

{% block head %}
<!-- Prevent caching for dynamic content -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
{% endblock %}

{% block content %}
<!-- Minimal Header Space -->
<div style="height: 80px;"></div>

<!-- Main Content -->
<main class="apple-container" style="padding-top: 0; max-width: 1600px;">
    <!-- Maximized Camera Section with Enhanced Box -->
    <section style="margin: 0; display: flex; justify-content: center; align-items: center; min-height: 70vh;">
        <!-- Enhanced Camera Container Box -->
        <div class="camera-main-container">
            <div class="camera-section-header">
                <h2 style="text-align: center; color: var(--color-text-white); font-size: 32px; margin-bottom: var(--spacing-lg); text-shadow: 0 4px 20px rgba(0,0,0,0.3); color: rgb(10, 10, 65);">
                    📸 Chụp Ảnh Business Card & Khuôn Mặt
                </h2>
            </div>

            <div class="camera-grid-enhanced">
                <!-- Camera 0: Business Card (LEFT) -->
                <div class="camera-container-enhanced">
                    <div class="camera-header-enhanced">
                        <div class="camera-title-section">
                            <div class="camera-icon">📄</div>
                            <div class="camera-title-text">
                                <h3>Business Card</h3>
                                <p>Đặt business card vào khung hình</p>
                            </div>
                        </div>
                        <div class="status-indicator-enhanced">
                            <div id="status0" class="status-dot status-ready"></div>
                            <span id="cardStatus">Camera sẵn sàng</span>
                        </div>
                    </div>
                    <div class="camera-frame-enhanced">
                        <!-- Hybrid camera support: video for WebRTC, img for server fallback -->
                        <video id="cam0-webrtc" autoplay muted playsinline class="camera-stream-enhanced" style="display: none;"></video>
                        <img id="cam0" src="/video_feed/0" alt="Business Card Camera" class="camera-stream-enhanced">
                        <div class="camera-overlay-guides">
                            <div class="guide-corner tl"></div>
                            <div class="guide-corner tr"></div>
                            <div class="guide-corner bl"></div>
                            <div class="guide-corner br"></div>
                        </div>
                    </div>
                </div>

                <!-- Camera 1: Face (RIGHT) -->
                <div class="camera-container-enhanced">
                    <div class="camera-header-enhanced">
                        <div class="camera-title-section">
                            <div class="camera-icon">👤</div>
                            <div class="camera-title-text">
                                <h3>Khuôn Mặt</h3>
                                <p>Đặt khuôn mặt vào khung hình</p>
                            </div>
                        </div>
                        <div class="status-indicator-enhanced">
                            <div id="status1" class="status-dot status-ready"></div>
                            <span id="faceStatus">Camera sẵn sàng</span>
                        </div>
                    </div>
                    <div class="camera-frame-enhanced">
                        <!-- Hybrid camera support: video for WebRTC, img for server fallback -->
                        <video id="cam1-webrtc" autoplay muted playsinline class="camera-stream-enhanced" style="display: none;"></video>
                        <img id="cam1" src="/video_feed/1" alt="Face Camera" class="camera-stream-enhanced">
                        <div class="camera-overlay-face">
                            <div class="face-guide-oval"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Compact Control Section -->
    <section style="margin: var(--spacing-lg) 0;">
        <div class="text-center">
            <div class="apple-button-group">
                <button class="apple-button apple-button-primary" id="singleCaptureBtn" onclick="singleCapture()" style="font-size: 18px; padding: 16px 32px;">
                    <span style="font-size: 20px;">📸</span>
                    Chụp ảnh
                </button>
                <button class="apple-button apple-button-secondary" id="retakeBtn" onclick="retakeAll()" style="display: none; font-size: 16px;">
                    <span style="font-size: 18px;">🔄</span>
                    Chụp lại
                </button>
            </div>
        </div>
    </section>

    <!-- Compact AI Generation Section -->
    <section style="margin: var(--spacing-lg) 0;">
        <div class="apple-card" style="max-width: 600px; margin: 0 auto; border: 2px solid rgba(255,255,255,0.3);">
            <div class="apple-card-header" style="background: var(--success-gradient); color: white; text-align: center; padding: var(--spacing-lg);">
                <h3 style="color: white; font-size: 20px; margin: 0; font-family: 'Nunito', sans-serif; font-weight: var(--font-weight-semibold);">🎨 Cấu hình AI</h3>
            </div>

            <div class="apple-card-body" style="padding: var(--spacing-lg);">
                <div style="margin-bottom: var(--spacing-md);">
                    <label style="display: block; margin-bottom: var(--spacing-sm); font-weight: var(--font-weight-medium); font-size: 16px; color: var(--color-gray-800); font-family: 'Nunito', sans-serif;">
                        Style ảnh:
                    </label>
                    <select id="promptSelect" class="apple-select" style="font-size: 16px; padding: 12px; border: 2px solid rgba(255,255,255,0.4); background: var(--bg-card); border-radius: var(--radius-lg); font-family: 'Nunito', sans-serif;">
                        <option value="prompt">Đang tải...</option>
                    </select>
                </div>

                <div id="promptDescription" style="padding: var(--spacing-md); background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%); border-radius: var(--radius-lg); font-size: 14px; color: var(--color-gray-800); border: 1px solid rgba(67, 233, 123, 0.3); font-family: 'Nunito', sans-serif; margin-bottom: var(--spacing-md); min-height: 40px; display: flex; align-items: center;">
                    Chọn style để xem mô tả
                </div>

                <div class="text-center">
                    <button class="apple-button apple-button-primary" id="generateBtn" onclick="generateAI()" disabled style="font-size: 18px; padding: 16px 32px; background: var(--warning-gradient);">
                        <span style="font-size: 20px;">✨</span>
                        Tạo ảnh AI
                    </button>
                </div>

                <!-- Progress -->
                <div id="progressBar" style="display: none; margin-top: var(--spacing-md);">
                    <div class="apple-progress-bar" style="height: 6px; background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%); border-radius: var(--radius-sm);">
                        <div class="apple-progress-fill" id="progressFill" style="background: var(--primary-gradient); height: 100%;"></div>
                    </div>
                    <p class="text-center" style="font-size: 14px; color: var(--color-gray-800); margin-top: var(--spacing-sm); font-family: 'Nunito', sans-serif;">
                        Đang xử lý với Gemini 2.0...
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Status Messages -->
    <div class="text-center">
        <div id="statusMessage" style="font-size: 16px; color: var(--color-text-secondary);"></div>
    </div>
</main>
{% endblock %}



