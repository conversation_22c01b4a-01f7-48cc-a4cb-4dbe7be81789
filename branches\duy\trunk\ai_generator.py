"""
AI Image Generator - T<PERSON><PERSON><PERSON> tạo ảnh AI sử dụng Gemini 2.0
Chỉ sử dụng prompt từ prompts/prompt.txt
"""

import os  # Thư viện hệ điều hành
import base64  # Thư viện mã hóa base64 cho ảnh
import time  # Thư viện thời gian
from datetime import datetime  # Thư viện ngày tháng
from pathlib import Path  # Thư viện đường dẫn file
from PIL import Image, ImageDraw, ImageFont  # Thư viện xử lý ảnh
from io import BytesIO  # Thư viện xử lý dữ liệu nhị phân

# Import path manager để xử lý đường dẫn tập trung - Import path manager for centralized path handling
from utils.path_manager import path_manager

# Import Google GenAI SDK mới cho Gemini 2.0 - Import new Google GenAI SDK for Gemini 2.0 (Fixed according to guide)
try:
    from google import genai  # SDK mới của Google
    from google.genai import types  # C<PERSON>c kiểu dữ liệu của SDK
    NEW_SDK = True  # Đánh dấu đang sử dụng SDK mới
    print("✅ Using new Google GenAI SDK for Gemini 2.0 (Fixed)")
except ImportError as e:
    print(f"❌ Failed to import new SDK: {e}")
    try:
        import google.generativeai as genai  # SDK cũ của Google
        NEW_SDK = False  # Đánh dấu đang sử dụng SDK cũ
        print("⚠️ Using legacy Google GenerativeAI SDK")
    except ImportError:
        print("❌ No Google AI SDK found. Please install google-generativeai or google-genai")
        genai = None  # Không có SDK nào
        NEW_SDK = False

from dotenv import load_dotenv  # Thư viện đọc biến môi trường
from ai_config import load_prompt, get_gemini_config  # Import cấu hình AI

# Tải biến môi trường - Load environment variables
load_dotenv()

class AIImageGenerator:
    """
    Trình tạo ảnh AI - Giai đoạn 2 của Two-Stage AI Pipeline
    AI Image Generator - Stage 2 of Two-Stage AI Pipeline

    Sử dụng Gemini 2.0 Flash Preview Image Generation để tạo ảnh AI với khả năng đa phương tiện:
    Uses Gemini 2.0 Flash Preview Image Generation specifically for creating AI images
    with multimodal capabilities:
    - Kết hợp template prompt, thông tin OCR và ảnh khuôn mặt tham chiếu
    - Combines prompt template, OCR information, and reference face image
    - Tạo nhiều biến thể ảnh với metadata phù hợp
    - Generates multiple image variants with proper metadata
    - Triển khai xử lý lỗi và cơ chế dự phòng
    - Implements error handling and fallback mechanisms
    """

    def __init__(self):
        """Khởi tạo AI Image Generator"""
        self.setup_gemini()  # Thiết lập kết nối Gemini API
        print("🔥 AI Image Generator initialized")
        print("   Model: Gemini 2.0 Flash Preview Image Generation")
        print("   Purpose: Stage 2 - AI Image Generation")
        print("   Features: Multimodal input, multiple variants, metadata tracking")
        print("   Prompt source: prompts/prompt.txt")

    def setup_gemini(self):
        """Thiết lập Gemini API - Setup Gemini API (Fixed according to guide)"""
        try:
            # Tải API key từ biến môi trường - Load API key from environment
            self.load_api_key()  # Gọi hàm tải API key

            if not self.gemini_key:  # Kiểm tra API key có tồn tại không
                raise ValueError("Gemini API key not found")  # Ném lỗi nếu không có key

            if genai is None:  # Kiểm tra SDK có khả dụng không
                raise ValueError("No Google AI SDK available")  # Ném lỗi nếu không có SDK

            # Đặt tên model cho Gemini 2.0 Flash Preview Image Generation
            # Set model name for Gemini 2.0 Flash Preview Image Generation
            self.model_name = "gemini-2.0-flash-preview-image-generation"  # Tên model chính thức

            # Cấu hình dựa trên loại SDK - Configure based on SDK type (according to guide)
            if NEW_SDK:  # Nếu sử dụng SDK mới
                # Sử dụng SDK mới với thiết lập client phù hợp - Use new SDK with proper client setup
                self.client = genai.Client(api_key=self.gemini_key)  # Tạo client với API key
                print(f"✅ Gemini 2.0 API configured with new SDK")  # Log cấu hình SDK mới
                print(f"   Model: {self.model_name}")  # Log tên model
                print(f"   Target: 30 seconds generation time")  # Log thời gian target
                print(f"   Fixed: Response modalities issue")  # Log đã fix issue
            else:  # Nếu sử dụng SDK cũ
                # Sử dụng SDK cũ làm dự phòng - Use legacy SDK as fallback
                genai.configure(api_key=self.gemini_key)  # Cấu hình API key cho SDK cũ
                self.model = genai.GenerativeModel(self.model_name)  # Tạo model với SDK cũ
                print(f"✅ Gemini 2.0 API configured with legacy SDK")  # Log cấu hình SDK cũ
                print(f"   Model: {self.model_name}")  # Log tên model

            self.config = get_gemini_config()  # Lấy cấu hình Gemini từ ai_config

        except Exception as e:
            print(f"❌ Gemini API setup error: {e}")  # Log lỗi thiết lập API
            raise  # Ném lại exception để caller xử lý

    def load_api_key(self):
        """Tải Gemini API key từ biến môi trường - Load Gemini API key from environment (Fixed according to guide)"""
        # Lấy tất cả API key có sẵn từ environment variables - Get all available API keys
        api_keys = [
            os.getenv('GEMINI_API_KEY'),  # API key chính từ .env
            os.getenv('GEMINI_API_KEY_2'),  # API key dự phòng 1 từ .env
            os.getenv('GEMINI_API_KEY_3')  # API key dự phòng 2 từ .env
        ]

        # Lọc bỏ key None/rỗng và key placeholder - Filter out None/empty keys and placeholder keys
        valid_keys = [key for key in api_keys if key and key.strip() and not key.startswith('YOUR_')]  # Chỉ lấy key hợp lệ

        if not valid_keys:  # Nếu không có key hợp lệ nào
            self.gemini_key = ''  # Đặt key chính rỗng
            self.backup_keys = []  # Danh sách key dự phòng rỗng
            print("⚠️ No valid Gemini API key found")  # Log cảnh báo không có key
            return  # Thoát khỏi function

        # Sử dụng key hợp lệ đầu tiên làm chính - Use first valid key as primary
        self.gemini_key = valid_keys[0]  # Gán key đầu tiên làm key chính
        self.backup_keys = valid_keys[1:] if len(valid_keys) > 1 else []  # Các key còn lại làm dự phòng

        print(f"✅ Gemini API key loaded for image generation")  # Log tải key thành công
        print(f"   API keys available: {len(valid_keys)}")  # Log số lượng key có sẵn
        print(f"   Primary key: {self.gemini_key[:20]}...")  # Log key chính (ẩn phần sau để bảo mật)
        if self.backup_keys:  # Nếu có key dự phòng
            print(f"   Backup keys available: {len(self.backup_keys)}")  # Log số lượng key dự phòng

    def switch_to_backup_key(self):
        """Chuyển sang API key dự phòng khi bị giới hạn tốc độ - Switch to backup API key when rate limit hit"""
        if not self.backup_keys:  # Nếu không có key dự phòng nào
            print("   ❌ No backup keys available")  # Log không có key dự phòng
            return False  # Trả về False để báo thất bại

        # Chuyển key hiện tại vào cuối danh sách dự phòng - Move current key to end of backup list
        self.backup_keys.append(self.gemini_key)  # Thêm key hiện tại vào cuối danh sách dự phòng

        # Sử dụng key dự phòng đầu tiên làm key chính mới - Use first backup key as new primary
        self.gemini_key = self.backup_keys.pop(0)  # Lấy key đầu tiên từ danh sách dự phòng

        # Cấu hình lại Gemini với key mới dựa trên loại SDK - Reconfigure Gemini with new key based on SDK type
        if NEW_SDK:  # Nếu sử dụng SDK mới
            self.client = genai.Client(api_key=self.gemini_key)  # Tạo client mới với key mới
        else:  # Nếu sử dụng SDK cũ
            genai.configure(api_key=self.gemini_key)  # Cấu hình lại API key cho SDK cũ

        print(f"   🔄 Switched to backup key: {self.gemini_key[:20]}...")  # Log chuyển sang key dự phòng
        print(f"   📊 Remaining backup keys: {len(self.backup_keys)}")  # Log số key dự phòng còn lại

        return True  # Trả về True để báo chuyển key thành công

    def generate_dollhouse_image(self, face_path, card_info, ai_config=None):
        """
        GIAI ĐOẠN 2: Tạo ảnh AI sử dụng Gemini 2.0 Flash + dữ liệu OCR + template prompt
        STAGE 2: Generate AI image using Gemini 2.0 Flash + OCR data + prompt template

        Kết hợp - Combines:
        1. Template prompt (phong cách cartoon/luxury/artistic)
        2. Thông tin OCR từ Gemini 2.5 Flash (Giai đoạn 1)
        3. Ảnh khuôn mặt tham chiếu
        """
        try:
            print(f"\n🎨 STAGE 2: AI Image Generation (Gemini 2.0 Flash)")  # Log giai đoạn 2
            print(f"   Face Image: {Path(face_path).name}")  # Log tên file ảnh khuôn mặt
            print(f"   Person: {card_info.get('name', 'Unknown Person')}")  # Log tên người
            print(f"   Company: {card_info.get('company', 'Unknown Company')}")  # Log tên công ty

            # Lấy template prompt từ ai_config - Get prompt template from ai_config
            prompt_template = 'prompt'  # Mặc định - default
            if ai_config and 'prompt_template' in ai_config:  # Nếu có config và template
                prompt_template = ai_config['prompt_template']  # Lấy template từ config

            # Tải template prompt THUẦN TÚY từ file - Load PURE prompt template from file
            pure_prompt = load_prompt(prompt_template)  # Tải prompt thuần túy từ file

            # Thay thế placeholder bằng thông tin thực từ OCR - Replace placeholders with actual OCR data
            final_prompt = self._replace_placeholders_only(pure_prompt, card_info)  # Chỉ thay thế placeholder

            print(f"   🎯 Style Template: {prompt_template}")  # Log template style
            print(f"   📝 Pure Prompt: {len(pure_prompt)} characters")  # Log độ dài prompt thuần túy
            print(f"   ✨ Final Prompt: {len(final_prompt)} characters")  # Log độ dài prompt cuối cùng
            print(f"   🔒 PURE MODE: Only using content from {prompt_template}.txt file")  # Log chế độ thuần túy

            # Tải ảnh khuôn mặt - Load face image
            if not os.path.exists(face_path):  # Nếu file ảnh không tồn tại
                raise FileNotFoundError(f"Face image not found: {face_path}")  # Ném lỗi file không tìm thấy

            face_image = Image.open(face_path)  # Mở ảnh khuôn mặt
            print(f"   Face image loaded: {face_image.size}")  # Log kích thước ảnh

            # Tạo ảnh với nhiều API (chuỗi fallback) - Generate với multiple APIs (fallback chain)
            images_generated = []  # Danh sách ảnh đã tạo

            for variant in range(1, 3):  # Tạo 2 biến thể - Tạo 2 variants
                try:
                    print(f"🎨 Generating variant {variant}/2...")  # Log đang tạo biến thể

                    # Thử Gemini 2.0 Flash Image Generation TRƯỚC (AI THẬT) - Try Gemini 2.0 Flash Image Generation FIRST (REAL AI)
                    result = self._generate_with_gemini20(final_prompt, face_path, card_info, variant)  # Gọi hàm tạo với Gemini 2.0

                    if result.get('success'):  # Nếu tạo thành công
                        images_generated.append(result['image_path'])  # Thêm đường dẫn ảnh vào danh sách
                        print(f"✅ Variant {variant} successful with Gemini 2.0 Flash AI: {result['image_path']}")  # Log thành công
                    else:  # Nếu tạo thất bại
                        print(f"❌ Gemini 2.0 Flash failed for variant {variant}: {result.get('error')}")  # Log thất bại

                        # Fallback sang Stable Diffusion - Fallback to Stable Diffusion
                        print(f"🔄 Trying Stable Diffusion for variant {variant}...")  # Log thử SD
                        result = self._generate_with_stable_diffusion(final_prompt, face_image, card_info, variant)  # Gọi SD

                        if result.get('success'):  # Nếu SD thành công
                            images_generated.append(result['image_path'])  # Thêm ảnh vào danh sách
                            print(f"✅ Variant {variant} successful with Stable Diffusion: {result['image_path']}")  # Log thành công SD
                        else:  # Nếu SD thất bại
                            print(f"❌ Stable Diffusion also failed for variant {variant}")  # Log SD thất bại

                            # Thử các model SD thay thế - Try alternative Stable Diffusion models
                            print(f"🔄 Trying alternative SD models for variant {variant}...")  # Log thử model thay thế
                            try:
                                result = self._generate_with_alternative_sd(final_prompt, face_image, card_info, variant)  # Gọi SD thay thế
                                if result.get('success'):  # Nếu thành công
                                    images_generated.append(result['image_path'])  # Thêm ảnh vào danh sách
                                    print(f"✅ Variant {variant} successful with Alternative SD: {result['image_path']}")  # Log thành công
                                else:
                                    # Phương án cuối cùng: Enhanced PIL - Last resort: Enhanced PIL
                                    print(f"🔄 Last resort - Enhanced PIL for variant {variant}...")  # Log phương án cuối
                                    result = self._create_ai_enhanced_dollhouse(face_image, card_info, variant, final_prompt)  # Tạo ảnh với PIL

                                    if result.get('success'):  # Nếu PIL thành công
                                        images_generated.append(result['image_path'])  # Thêm ảnh vào danh sách
                                        print(f"✅ Variant {variant} successful with Enhanced PIL: {result['image_path']}")  # Log thành công PIL
                                    else:
                                        print(f"❌ Variant {variant} completely failed")  # Log thất bại hoàn toàn
                            except Exception as fallback_error:
                                print(f"❌ Alternative SD error: {fallback_error}")  # Log lỗi SD thay thế
                                # Phương án cuối cùng: Enhanced PIL - Last resort: Enhanced PIL
                                print(f"🔄 Final fallback - Enhanced PIL for variant {variant}...")  # Log fallback cuối cùng
                                result = self._create_ai_enhanced_dollhouse(face_image, card_info, variant, final_prompt)  # Tạo ảnh với PIL

                                if result.get('success'):  # Nếu PIL thành công
                                    images_generated.append(result['image_path'])  # Thêm ảnh vào danh sách
                                    print(f"✅ Variant {variant} successful with Enhanced PIL: {result['image_path']}")  # Log thành công PIL
                                else:
                                    print(f"❌ Variant {variant} completely failed")  # Log thất bại hoàn toàn

                except Exception as e:
                    print(f"❌ Variant {variant} error: {str(e)}")  # Log lỗi biến thể
                    continue  # Tiếp tục với biến thể tiếp theo

            # Trả về kết quả với định dạng nhất quán - Return results with consistent format
            if images_generated:  # Nếu có ảnh được tạo
                return {
                    'success': True,  # Trạng thái thành công
                    'generated_images': images_generated,  # Tên field nhất quán - Consistent field name
                    'image_paths': images_generated,       # Tương thích legacy - Legacy compatibility
                    'image_path': images_generated[0],     # Ảnh chính - Primary image
                    'api_used': 'Gemini 2.0 Flash Preview',  # API đã sử dụng
                    'model_used': self.config['model'],  # Model đã sử dụng
                    'generation_time': f"{len(images_generated)} variants",  # Thời gian tạo
                    'prompt_source': 'prompts/prompt.txt',  # Nguồn prompt
                    'variants_count': len(images_generated)  # Số lượng biến thể
                }
            else:  # Nếu không có ảnh nào được tạo
                return {
                    'success': False,  # Trạng thái thất bại
                    'error': 'No images generated successfully',  # Thông báo lỗi
                    'generated_images': [],  # Danh sách ảnh rỗng
                    'variants_count': 0  # Số biến thể = 0
                }

        except Exception as e:
            print(f"❌ Generation failed: {str(e)}")  # Log tạo ảnh thất bại
            return {
                'success': False,  # Trạng thái thất bại
                'error': str(e)  # Thông báo lỗi
            }

    def _replace_placeholders_only(self, base_prompt, card_info):
        """
        CHỈ THAY THẾ PLACEHOLDER trong prompt với dữ liệu OCR - ONLY replace placeholders in prompt with OCR data

        Không thêm bất kỳ nội dung nào khác vào prompt gốc
        Does not add any other content to the original prompt

        Chỉ thay thế các placeholder như [name], [company], [occupation], [gmail], [phone number]
        Only replaces placeholders like [name], [company], [occupation], [gmail], [phone number]
        """

        # Trích xuất thông tin từ OCR - Extract information from OCR
        name = card_info.get('name', 'Professional').strip()  # Lấy tên hoặc mặc định
        title = card_info.get('title', card_info.get('occupation', 'Professional')).strip()  # Lấy chức vụ
        company = card_info.get('company', 'Company').strip()  # Lấy công ty
        email = card_info.get('email', card_info.get('gmail', '<EMAIL>')).strip()  # Lấy email
        phone = card_info.get('phone', card_info.get('phone_number', '+84 xxx xxx xxx')).strip()  # Lấy số điện thoại

        # CHỈ THAY THẾ PLACEHOLDER - ONLY replace placeholders
        final_prompt = base_prompt
        final_prompt = final_prompt.replace('[name]', name)
        final_prompt = final_prompt.replace('[occupation]', title)
        final_prompt = final_prompt.replace('[company]', company)
        final_prompt = final_prompt.replace('[gmail]', email)
        final_prompt = final_prompt.replace('[phone number]', phone)

        print(f"   🔄 Replaced placeholders: name={name}, title={title}, company={company}")  # Log thay thế
        print(f"   🔒 PURE MODE: No additional content added to prompt")  # Log chế độ thuần túy

        return final_prompt  # Trả về prompt với placeholder đã thay thế

    def _generate_with_gemini20(self, prompt, face_image, card_info, variant):
        """
        Tạo ảnh AI sử dụng Gemini 2.0 Flash Preview Image Generation
        Generate AI image using Gemini 2.0 Flash Preview Image Generation

        Kết hợp - Combines:
        - PURE prompt (chỉ nội dung từ file .txt đã chọn với placeholder được thay thế)
        - Ảnh khuôn mặt tham chiếu - Face reference image
        - Khả năng tạo đa phương tiện - Multimodal generation capabilities
        """
        try:
            print(f"🔥 Gemini 2.0 Flash Preview Generation (variant {variant})...")  # Log bắt đầu tạo với Gemini 2.0
            print(f"   🎯 Using PURE prompt from selected file")  # Log sử dụng prompt thuần túy
            print(f"   👤 Person: {card_info.get('name', 'Unknown')}")  # Log tên người
            print(f"   🏢 Company: {card_info.get('company', 'Unknown')}")  # Log công ty

            # Tải và validate ảnh khuôn mặt - Load and validate face image
            face_path = face_image if isinstance(face_image, str) else None  # Lấy đường dẫn ảnh nếu là string
            if face_path and os.path.exists(face_path):  # Nếu có đường dẫn và file tồn tại
                face_img = Image.open(face_path)  # Mở ảnh từ đường dẫn
                print(f"   📸 Face image loaded: {face_img.size}")  # Log kích thước ảnh đã tải
            else:  # Nếu không có đường dẫn hoặc file không tồn tại
                face_img = face_image  # Sử dụng ảnh được cung cấp trực tiếp
                print(f"   📸 Using provided face image")  # Log sử dụng ảnh được cung cấp

            # Sử dụng prompt THUẦN TÚY từ file đã chọn - Use PURE prompt from selected file
            if prompt and prompt.strip():  # Nếu có prompt và không rỗng
                final_prompt = prompt.strip()  # Loại bỏ khoảng trắng thừa
                print(f"🎯 Using PURE prompt from file: {len(final_prompt)} characters")  # Log độ dài prompt
                print(f"   📝 PURE content from selected .txt file only")  # Log nguồn prompt thuần túy

                # Hiển thị thông tin đã thay thế placeholder - Show replaced placeholder info
                name = card_info.get('name', '')  # Lấy tên từ card info
                company = card_info.get('company', '')  # Lấy công ty từ card info
                if name or company:  # Nếu có tên hoặc công ty
                    print(f"   🔄 Placeholders replaced: Name='{name}', Company='{company}'")  # Log thông tin placeholder đã thay thế
            else:  # Nếu không có prompt
                # KHÔNG CÓ FALLBACK - Báo lỗi nếu không đọc được file prompt
                raise ValueError("❌ PURE MODE: Cannot read prompt file. Please check prompts/ folder and selected template.")  # Báo lỗi không có prompt

            # Quy trình tạo ảnh cốt lõi (Đã sửa theo hướng dẫn) - Core Generation Process (Fixed according to guide)
            if NEW_SDK:  # Nếu sử dụng SDK mới
                # Chuyển đổi ảnh PIL thành bytes cho SDK mới - Convert PIL image to bytes for new SDK
                from io import BytesIO  # Import BytesIO để xử lý dữ liệu nhị phân
                img_byte_arr = BytesIO()  # Tạo buffer nhị phân
                face_img.save(img_byte_arr, format='JPEG')  # Lưu ảnh vào buffer dưới dạng JPEG
                img_byte_arr = img_byte_arr.getvalue()  # Lấy dữ liệu bytes từ buffer

                print(f"🎯 Creating multimodal content for Gemini 2.0...")  # Log tạo nội dung đa phương tiện
                print(f"   Text prompt: {len(final_prompt)} characters")  # Log độ dài prompt
                print(f"   Image data: {len(img_byte_arr)} bytes")  # Log kích thước dữ liệu ảnh

                # Tạo nội dung cho Gemini 2.0 (chính xác theo hướng dẫn) - Create content for Gemini 2.0 (exactly as in guide)
                contents = [
                    types.Part.from_text(text=final_prompt),  # Phần text từ prompt
                    types.Part.from_bytes(data=img_byte_arr, mime_type="image/jpeg")  # Phần ảnh từ bytes
                ]

                print("🔥 Generating with Gemini 2.0 Flash Preview Image Generation...")  # Log bắt đầu tạo ảnh
                response = self.client.models.generate_content(  # Gọi API tạo nội dung
                    model=self.model_name,  # Tên model
                    contents=contents,  # Nội dung đầu vào
                    config=types.GenerateContentConfig(  # Cấu hình tạo
                        response_modalities=['TEXT', 'IMAGE']  # Yêu cầu cốt lõi từ hướng dẫn - Core requirement from guide
                    )
                )
                print(f"✅ Response received from Gemini 2.0 Flash")  # Log nhận được response
            else:  # Nếu sử dụng SDK cũ
                # Sử dụng SDK cũ - chỉ tạo text hiện tại - Use legacy SDK - text only generation for now
                print("🔄 Using legacy SDK (text-only generation)...")  # Log sử dụng SDK cũ
                print("⚠️ Legacy SDK doesn't support image generation, using fallback...")  # Log SDK cũ không hỗ trợ tạo ảnh
                return {'success': False, 'error': 'Legacy SDK image generation not supported'}  # Trả về lỗi

            print(f"📊 Response received: {type(response)}")  # Log loại response nhận được

            # Kiểm tra xem response có dữ liệu ảnh không - Check if response has image data
            image_data = None  # Khởi tạo dữ liệu ảnh None
            text_response = "Image generated successfully"  # Response text mặc định

            if hasattr(response, 'candidates') and response.candidates:  # Nếu response có candidates
                candidate = response.candidates[0]  # Lấy candidate đầu tiên
                if hasattr(candidate, 'content') and candidate.content:  # Nếu candidate có content
                    content = candidate.content  # Lấy content
                    if hasattr(content, 'parts') and content.parts:  # Nếu content có parts
                        for part in content.parts:  # Lặp qua từng part
                            if hasattr(part, 'inline_data') and part.inline_data:  # Nếu part có inline_data
                                image_data = part.inline_data.data  # Lấy dữ liệu ảnh
                                print("✅ Found image data in response")  # Log tìm thấy dữ liệu ảnh
                                break  # Thoát khỏi vòng lặp
                            elif hasattr(part, 'text') and part.text:  # Nếu part có text
                                text_response = part.text  # Lấy text response
                                print(f"📝 Text response: {text_response[:100]}...")  # Log text response (100 ký tự đầu)

            # Kiểm tra response trực tiếp cho dữ liệu ảnh - Check direct response for image data
            if not image_data and hasattr(response, 'parts'):  # Nếu chưa có dữ liệu ảnh và response có parts
                for part in response.parts:  # Lặp qua từng part trong response
                    if hasattr(part, 'inline_data') and part.inline_data:  # Nếu part có inline_data
                        image_data = part.inline_data.data  # Lấy dữ liệu ảnh
                        print("✅ Found image data in direct response")  # Log tìm thấy dữ liệu ảnh trong response trực tiếp
                        break  # Thoát khỏi vòng lặp

            # Process image data if found
            if image_data:
                print(f"🖼️ Processing image data: {len(image_data)} bytes")

                # Decode and save image (Fixed according to guide)
                try:
                    import base64
                    # Check if data is already bytes or base64 string
                    if isinstance(image_data, str):
                        image_bytes = base64.b64decode(image_data)
                    else:
                        image_bytes = image_data

                    # Create BytesIO object and open image
                    image_buffer = BytesIO(image_bytes)
                    image = Image.open(image_buffer)
                    print(f"✅ Image decoded successfully: {image.size}")
                except Exception as decode_error:
                    print(f"❌ Image decode error: {decode_error}")
                    # Try alternative decode methods
                    try:
                        # Method 2: Direct bytes
                        image = Image.open(BytesIO(image_data))
                        print(f"✅ Alternative decode successful: {image.size}")
                    except Exception as alt_error:
                        print(f"❌ Alternative decode failed: {alt_error}")
                        raise decode_error

                # Save image using PathManager
                person_name = card_info.get('name', 'person')
                output_path = path_manager.get_ai_image_path(person_name, variant, prefix="gemini_ai")

                image.save(output_path)
                print(f"✅ REAL AI image saved: {output_path}")

                return {
                    'success': True,
                    'image_path': str(output_path),
                    'size': image.size,
                    'type': 'gemini_real_ai'
                }
            else:
                # If no image found
                print("⚠️ No image data found in Gemini response")
                if hasattr(response, 'text') and response.text:
                    print(f"📝 Response text: {response.text[:200]}...")

                # Fallback to Stable Diffusion
                print("🤗 Falling back to Stable Diffusion...")
                return self._generate_with_stable_diffusion(prompt, face_image, card_info, variant)

        except Exception as e:
            print(f"   ❌ Gemini Image Generation error: {e}")

            # Check if it's a rate limit error
            error_str = str(e).lower()
            if 'rate limit' in error_str or 'quota' in error_str or '429' in error_str:
                print("   🔄 Rate limit detected, trying backup key...")

                if self.switch_to_backup_key():
                    # Retry with backup key
                    try:
                        print("   🔄 Retrying with backup key...")
                        return self._generate_with_gemini20(prompt, face_image, card_info, variant)
                    except Exception as retry_error:
                        print(f"   ❌ Backup key also failed: {retry_error}")
                else:
                    print("   ❌ No backup keys available")

            import traceback
            traceback.print_exc()

            # Fallback to Stable Diffusion
            print("   🤗 Exception fallback to Stable Diffusion...")
            return self._generate_with_stable_diffusion(prompt, face_image, card_info, variant)



        except Exception as e:
            print(f"   ❌ Exception in _generate_with_gemini20: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_ai_enhanced_dollhouse(self, face_image, card_info, variant, ai_description):
        """Create enhanced dollhouse using AI description"""
        try:
            print(f"   🎨 Creating AI-enhanced dollhouse (variant {variant})")

            # Create high-resolution base image with realistic dollhouse aesthetic
            width, height = 1024, 768
            image = Image.new('RGB', (width, height), '#1A1A1A')  # Deep black background
            draw = ImageDraw.Draw(image)

            # Create realistic dollhouse room with perspective
            self._create_realistic_room_background(draw, width, height)

            # Add professional lighting effects
            self._add_professional_lighting(draw, width, height)

            # Add room walls with perspective
            wall_color = '#8B4513'  # Saddle brown
            # Back wall
            draw.polygon([(50, 50), (width-50, 50), (width-100, 150), (100, 150)], fill=wall_color)
            # Side walls
            draw.polygon([(50, 50), (100, 150), (100, height-50), (50, height-50)], fill='#654321')
            draw.polygon([(width-50, 50), (width-50, height-50), (width-100, height-50), (width-100, 150)], fill='#654321')

            # Add floor with wood texture
            floor_color = '#DEB887'  # Burlywood
            draw.rectangle([100, 150, width-100, height-50], fill=floor_color)

            # Add wood planks effect
            for i in range(5, width-100, 80):
                draw.line([(100+i, 150), (100+i, height-50)], fill='#CD853F', width=2)

            # Add professional furniture
            self._add_enhanced_furniture(draw, width, height)

            # Add nameplate on floor
            self._add_enhanced_nameplate(draw, card_info, width, height)

            # Add wall display
            self._add_enhanced_wall_display(draw, card_info, width, height)

            # Add face to dollhouse figure
            if face_image:
                self._add_face_to_dollhouse(image, face_image, card_info, width, height)

            # Add lighting effects
            self._add_lighting_effects(draw, width, height)

            # Save enhanced image using PathManager
            person_name = card_info.get('name', 'person')
            output_path = path_manager.get_ai_image_path(person_name, variant, prefix="ai_enhanced")

            image.save(output_path)
            print(f"   ✅ AI-enhanced dollhouse saved: {output_path}")

            return {
                'success': True,
                'image_path': str(output_path),
                'size': (width, height),
                'type': 'ai_enhanced_dollhouse'
            }

        except Exception as e:
            print(f"   ❌ Enhanced dollhouse error: {e}")
            # Fallback to basic dollhouse
            return self._create_realistic_dollhouse(face_image, card_info, variant, ai_description)

    def _add_enhanced_furniture(self, draw, width, height):
        """Add enhanced furniture to dollhouse"""
        # Professional desk (larger and more detailed)
        desk_x, desk_y = width//2 - 100, height//2 + 50
        desk_width, desk_height = 200, 80

        # Desk surface with wood grain
        draw.rectangle([desk_x, desk_y, desk_x + desk_width, desk_y + desk_height],
                      fill='#8B4513', outline='#654321', width=3)

        # Desk legs
        leg_positions = [(desk_x+10, desk_y+desk_height), (desk_x+desk_width-10, desk_y+desk_height),
                        (desk_x+10, desk_y+desk_height-60), (desk_x+desk_width-10, desk_y+desk_height-60)]
        for leg_x, leg_y in leg_positions:
            draw.rectangle([leg_x-5, leg_y, leg_x+5, leg_y+40], fill='#654321')

        # Office chair
        chair_x, chair_y = desk_x + desk_width + 30, desk_y + 20
        # Chair seat
        draw.ellipse([chair_x, chair_y, chair_x+40, chair_y+30], fill='#000080')
        # Chair back
        draw.rectangle([chair_x+5, chair_y-25, chair_x+35, chair_y+5], fill='#000080')
        # Chair base
        draw.line([chair_x+20, chair_y+30, chair_x+20, chair_y+50], fill='#C0C0C0', width=8)

        # Computer monitor
        monitor_x, monitor_y = desk_x + 50, desk_y - 40
        draw.rectangle([monitor_x, monitor_y, monitor_x+60, monitor_y+45], fill='#000000', outline='#C0C0C0', width=2)
        draw.rectangle([monitor_x+5, monitor_y+5, monitor_x+55, monitor_y+35], fill='#0066CC')  # Screen

        # Keyboard
        draw.rectangle([desk_x+40, desk_y+20, desk_x+120, desk_y+35], fill='#F5F5F5', outline='#000000')

        # Filing cabinet
        cabinet_x, cabinet_y = desk_x - 80, desk_y + 20
        draw.rectangle([cabinet_x, cabinet_y, cabinet_x+60, cabinet_y+100], fill='#696969', outline='#000000', width=2)
        # Cabinet drawers
        for i in range(3):
            draw.line([cabinet_x+5, cabinet_y+20+i*25, cabinet_x+55, cabinet_y+20+i*25], fill='#000000', width=2)
            draw.rectangle([cabinet_x+45, cabinet_y+10+i*25, cabinet_x+50, cabinet_y+15+i*25], fill='#FFD700')  # Handle

    def _add_enhanced_nameplate(self, draw, card_info, width, height):
        """Add enhanced nameplate on floor"""
        try:
            # Load font for nameplate
            try:
                font = ImageFont.truetype("arial.ttf", 24)
                small_font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
                small_font = ImageFont.load_default()

            name = card_info.get('name', 'Professional')

            # Nameplate background (golden)
            plate_x, plate_y = width//2 - 120, height - 150
            plate_width, plate_height = 240, 60

            # Golden nameplate with shadow
            draw.rectangle([plate_x+3, plate_y+3, plate_x+plate_width+3, plate_y+plate_height+3],
                          fill='#8B4513')  # Shadow
            draw.rectangle([plate_x, plate_y, plate_x+plate_width, plate_y+plate_height],
                          fill='#FFD700', outline='#B8860B', width=3)  # Golden plate

            # Name text
            text_bbox = draw.textbbox((0, 0), name, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = plate_x + (plate_width - text_width) // 2
            text_y = plate_y + 15

            # Text shadow
            draw.text((text_x+2, text_y+2), name, fill='#8B4513', font=font)
            # Main text
            draw.text((text_x, text_y), name, fill='#000000', font=font)

        except Exception as e:
            print(f"   ⚠️ Nameplate error: {e}")

    def _add_enhanced_wall_display(self, draw, card_info, width, height):
        """Add enhanced wall display"""
        try:
            # Load font
            try:
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()

            title = card_info.get('title', 'Professional')
            company = card_info.get('company', 'Company')

            # Wall display background
            display_x, display_y = width//2 - 150, 80
            display_width, display_height = 300, 80

            # Display background with frame
            draw.rectangle([display_x+2, display_y+2, display_x+display_width+2, display_y+display_height+2],
                          fill='#654321')  # Shadow
            draw.rectangle([display_x, display_y, display_x+display_width, display_y+display_height],
                          fill='#FFFFFF', outline='#8B4513', width=4)

            # Title text
            title_bbox = draw.textbbox((0, 0), title, font=font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = display_x + (display_width - title_width) // 2
            draw.text((title_x, display_y + 15), title, fill='#000080', font=font)

            # Company text
            company_bbox = draw.textbbox((0, 0), company, font=font)
            company_width = company_bbox[2] - company_bbox[0]
            company_x = display_x + (display_width - company_width) // 2
            draw.text((company_x, display_y + 45), company, fill='#8B0000', font=font)

        except Exception as e:
            print(f"   ⚠️ Wall display error: {e}")

    def _add_lighting_effects(self, draw, width, height):
        """Add lighting effects to dollhouse"""
        # Warm ceiling light
        light_x, light_y = width//2, 100

        # Light fixture
        draw.ellipse([light_x-15, light_y-10, light_x+15, light_y+10], fill='#FFD700', outline='#B8860B', width=2)

        # Light rays effect
        for i in range(8):
            angle = i * 45
            import math
            end_x = light_x + 50 * math.cos(math.radians(angle))
            end_y = light_y + 50 * math.sin(math.radians(angle))
            draw.line([light_x, light_y, end_x, end_y], fill='#FFFFE0', width=2)

        # Neon accent on wall
        neon_y = height//2
        for i in range(0, width-200, 20):
            color_intensity = int(128 + 127 * math.sin(i * 0.1))
            neon_color = (0, color_intensity, 255)
            draw.rectangle([100+i, neon_y-2, 100+i+10, neon_y+2], fill=neon_color)

    def _create_realistic_room_background(self, draw, width, height):
        """Create realistic dollhouse room with perspective and textures"""
        # Room dimensions with perspective
        floor_y = height - 100
        back_wall_y = 80

        # Floor with wood planks texture
        floor_color = '#8B4513'  # Saddle brown
        draw.rectangle([0, floor_y, width, height], fill=floor_color)

        # Add wood plank lines
        for i in range(0, width, 60):
            draw.line([(i, floor_y), (i, height)], fill='#654321', width=2)
        for i in range(floor_y, height, 40):
            draw.line([(0, i), (width, i)], fill='#654321', width=1)

        # Back wall with perspective
        wall_color = '#D2B48C'  # Tan
        wall_points = [
            (50, back_wall_y),      # Top left
            (width-50, back_wall_y), # Top right
            (width-20, floor_y),     # Bottom right
            (20, floor_y)            # Bottom left
        ]
        draw.polygon(wall_points, fill=wall_color, outline='#8B4513', width=3)

        # Side walls
        left_wall_points = [
            (0, back_wall_y + 30),
            (50, back_wall_y),
            (20, floor_y),
            (0, height)
        ]
        draw.polygon(left_wall_points, fill='#CD853F', outline='#8B4513', width=2)

        right_wall_points = [
            (width, back_wall_y + 30),
            (width-50, back_wall_y),
            (width-20, floor_y),
            (width, height)
        ]
        draw.polygon(right_wall_points, fill='#CD853F', outline='#8B4513', width=2)

        # Add wall texture lines
        for i in range(back_wall_y + 20, floor_y, 30):
            draw.line([(60, i), (width-60, i)], fill='#B8860B', width=1)

    def _add_professional_lighting(self, draw, width, height):
        """Add professional lighting effects"""
        # Ceiling light fixture
        light_x, light_y = width//2, 60

        # Main light fixture (chandelier style)
        draw.ellipse([light_x-20, light_y-15, light_x+20, light_y+15],
                    fill='#FFD700', outline='#B8860B', width=3)
        draw.ellipse([light_x-10, light_y-8, light_x+10, light_y+8],
                    fill='#FFFFE0', outline='#FFD700', width=2)

        # Light rays with gradient effect
        import math
        for angle in range(0, 360, 30):
            end_x = light_x + 80 * math.cos(math.radians(angle))
            end_y = light_y + 80 * math.sin(math.radians(angle))

            # Create gradient light ray
            for i in range(5):
                alpha = 255 - (i * 40)
                ray_color = (255, 255, 224, alpha) if i == 0 else (255, 255, 200)
                draw.line([light_x, light_y, end_x, end_y], fill=ray_color[:3], width=3-i)

        # Ambient lighting on walls
        for y in range(100, height-100, 20):
            intensity = int(200 - (y - 100) * 0.5)
            if intensity > 50:
                draw.line([(30, y), (50, y)], fill=(intensity, intensity-20, intensity-40), width=2)
                draw.line([(width-50, y), (width-30, y)], fill=(intensity, intensity-20, intensity-40), width=2)

        # Floor lighting reflection
        for x in range(100, width-100, 40):
            for y in range(height-80, height-20, 20):
                intensity = int(150 - abs(x - width//2) * 0.3)
                if intensity > 80:
                    draw.ellipse([x-5, y-2, x+5, y+2], fill=(intensity, intensity-10, intensity-30))









    def _generate_with_stable_diffusion(self, prompt, face_image, card_info, variant):
        """Generate image using FREE Hugging Face Inference API"""
        try:
            import requests
            import time

            print(f"   🎨 Trying FREE Hugging Face Stable Diffusion API (variant {variant})...")

            # Use FREE Hugging Face Inference API - no API key needed!
            API_URL = "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0"

            # Sử dụng prompt THUẦN TÚY từ file - Use PURE prompt from file
            pure_prompt = prompt.strip()  # Chỉ sử dụng prompt từ file, không thêm gì

            print(f"   📝 PURE prompt from file: {pure_prompt[:100]}...")
            print(f"   🔒 PURE MODE: No additional content added to Stable Diffusion prompt")

            # Get Hugging Face token from environment
            hf_token = os.getenv('HUGGINGFACE_TOKEN', '')
            headers = {}
            if hf_token:
                headers = {"Authorization": f"Bearer {hf_token}"}
                print(f"   🔑 Using Hugging Face token: {hf_token[:10]}...")
            else:
                print(f"   ⚠️ No Hugging Face token found, using free tier")

            # Make request to Hugging Face API
            def query(payload):
                response = requests.post(API_URL, headers=headers, json=payload, timeout=60)
                return response.content

            # Generate image
            image_bytes = query({
                "inputs": pure_prompt,
                "parameters": {
                    "num_inference_steps": 30,
                    "guidance_scale": 7.5,
                    "width": 1024,
                    "height": 768
                }
            })

            if image_bytes and len(image_bytes) > 1000:  # Valid image
                # Save generated image using PathManager
                person_name = card_info.get('name', 'person')
                output_path = path_manager.get_ai_image_path(person_name, variant, prefix="ai_dollhouse")

                with open(output_path, "wb") as f:
                    f.write(image_bytes)

                print(f"   ✅ Hugging Face AI image saved: {output_path}")

                return {
                    'success': True,
                    'image_path': str(output_path),
                    'size': (1024, 768),
                    'type': 'huggingface_ai'
                }
            else:
                print(f"   ❌ Invalid image response from Hugging Face")
                return {'success': False, 'error': 'Invalid image response'}

        except Exception as e:
            print(f"   ❌ Stable Diffusion error: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_with_alternative_sd(self, prompt, face_image, card_info, variant):
        """Generate image using alternative Stable Diffusion models"""
        try:
            import requests

            print(f"   🎨 Trying Alternative Stable Diffusion models (variant {variant})...")

            # Alternative models to try
            models = [
                "runwayml/stable-diffusion-v1-5",
                "CompVis/stable-diffusion-v1-4",
                "stabilityai/stable-diffusion-2-1"
            ]

            for model_name in models:
                try:
                    API_URL = f"https://api-inference.huggingface.co/models/{model_name}"
                    print(f"   🔄 Trying model: {model_name}")

                    # Sử dụng prompt THUẦN TÚY từ file - Use PURE prompt from file
                    pure_prompt = prompt.strip()  # Chỉ sử dụng prompt từ file, không thêm gì
                    print(f"   🔒 PURE MODE: Using only content from selected .txt file")

                    # Headers
                    headers = {}
                    token = os.getenv('HUGGINGFACE_TOKEN')
                    if token:
                        headers["Authorization"] = f"Bearer {token}"

                    # Make request
                    def query(payload):
                        response = requests.post(API_URL, headers=headers, json=payload, timeout=120)
                        return response.content

                    # Generate with better parameters
                    image_bytes = query({
                        "inputs": pure_prompt,
                        "parameters": {
                            "num_inference_steps": 50,
                            "guidance_scale": 8.0,
                            "width": 1024,
                            "height": 768,
                            "negative_prompt": "blurry, low quality, distorted, ugly, bad anatomy"
                        }
                    })

                    if image_bytes and len(image_bytes) > 1000:
                        # Save image using PathManager
                        person_name = card_info.get('name', 'person')
                        output_path = path_manager.get_ai_image_path(person_name, variant, prefix="alt_ai")

                        with open(output_path, "wb") as f:
                            f.write(image_bytes)

                        print(f"   ✅ Alternative AI image saved: {output_path}")

                        return {
                            'success': True,
                            'image_path': str(output_path),
                            'size': (1024, 768),
                            'type': 'alternative_ai',
                            'model': model_name
                        }
                    else:
                        print(f"   ❌ Invalid response from {model_name}")
                        continue

                except Exception as model_error:
                    print(f"   ❌ Model {model_name} failed: {model_error}")
                    continue

            return {'success': False, 'error': 'All alternative models failed'}

        except Exception as e:
            print(f"   ❌ Alternative SD error: {e}")
            return {'success': False, 'error': str(e)}



    def _create_realistic_dollhouse(self, face_image, card_info, variant, prompt):
        """Create a realistic dollhouse scene with face and profession elements"""
        try:

            # Create dollhouse scene
            width, height = 1024, 768

            # Create background with dollhouse perspective
            image = Image.new('RGB', (width, height), color='#f5f5dc')  # Beige background
            draw = ImageDraw.Draw(image)

            # Draw dollhouse room structure
            self._draw_dollhouse_room(draw, width, height)

            # Add profession-specific furniture
            self._add_profession_furniture(draw, card_info, width, height)

            # Add face to dollhouse figure
            self._add_face_to_dollhouse(image, face_image, card_info, width, height)

            # Add nameplate and company info
            self._add_nameplate_and_info(draw, card_info, width, height)

            # Save image using PathManager
            person_name = card_info.get('name', 'person')
            output_path = path_manager.get_ai_image_path(person_name, variant, prefix="dollhouse")

            image.save(output_path)
            print(f"   Realistic dollhouse saved: {output_path}")

            return {
                'success': True,
                'image_path': str(output_path),
                'size': image.size,
                'type': 'realistic_dollhouse'
            }

        except Exception as e:
            print(f"   Error creating realistic dollhouse: {e}")
            return {
                'success': False,
                'error': f"Realistic dollhouse creation failed: {str(e)}"
            }

    def _draw_dollhouse_room(self, draw, width, height):
        """Draw basic dollhouse room structure"""
        # Floor
        draw.rectangle([0, height*0.7, width, height], fill='#8B4513')  # Brown floor

        # Back wall
        draw.rectangle([0, 0, width, height*0.7], fill='#E6E6FA')  # Light purple wall

        # Side walls (perspective)
        points = [(0, 0), (width*0.2, height*0.15), (width*0.2, height*0.7), (0, height*0.7)]
        draw.polygon(points, fill='#D8BFD8')  # Darker purple side wall

        # Ceiling
        points = [(0, 0), (width, 0), (width*0.8, height*0.15), (width*0.2, height*0.15)]
        draw.polygon(points, fill='#F0F8FF')  # Light blue ceiling

    def _add_profession_furniture(self, draw, card_info, width, height):
        """Add furniture based on profession"""
        title = card_info.get('title', '').lower()

        # Desk (common for most professions)
        desk_x, desk_y = width*0.3, height*0.55
        desk_w, desk_h = width*0.4, height*0.15
        draw.rectangle([desk_x, desk_y, desk_x + desk_w, desk_y + desk_h], fill='#8B4513')

        # Chair
        chair_x, chair_y = width*0.25, height*0.5
        draw.rectangle([chair_x, chair_y, chair_x + width*0.1, chair_y + height*0.1], fill='#654321')

        # Profession-specific items
        if 'engineer' in title or 'manager' in title or 'tech' in title:
            # Computer/laptop
            draw.rectangle([desk_x + 20, desk_y - 30, desk_x + 80, desk_y - 10], fill='#2F4F4F')
            # Monitor
            draw.rectangle([desk_x + 100, desk_y - 50, desk_x + 200, desk_y], fill='#000000')
        elif 'doctor' in title or 'medical' in title:
            # Medical equipment
            draw.ellipse([desk_x + 50, desk_y - 20, desk_x + 70, desk_y], fill='#FF6347')
        elif 'teacher' in title or 'education' in title:
            # Books
            for i in range(3):
                draw.rectangle([desk_x + 30 + i*15, desk_y - 25, desk_x + 40 + i*15, desk_y],
                             fill=['#FF0000', '#00FF00', '#0000FF'][i])

    def _add_face_to_dollhouse(self, image, face_image, card_info, width, height):
        """Add face to dollhouse figure"""
        try:
            # Resize face for dollhouse scale
            face_size = (60, 80)
            face_resized = face_image.resize(face_size, Image.Resampling.LANCZOS)

            # Create circular mask for face
            mask = Image.new('L', face_size, 0)
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.ellipse([5, 5, face_size[0]-5, face_size[1]-5], fill=255)

            # Apply mask to face
            face_resized.putalpha(mask)

            # Position figure in dollhouse
            figure_x = int(width * 0.4)
            figure_y = int(height * 0.35)

            # Draw body (simple rectangle)
            body_draw = ImageDraw.Draw(image)
            body_draw.rectangle([figure_x, figure_y + 60, figure_x + 40, figure_y + 120], fill='#000080')  # Blue suit

            # Paste face
            image.paste(face_resized, (figure_x - 10, figure_y), face_resized)

        except Exception as e:
            print(f"   Error adding face: {e}")

    def _add_nameplate_and_info(self, draw, card_info, width, height):
        """Add nameplate and company information"""
        try:
            from PIL import ImageFont

            # Try to load font
            try:
                font = ImageFont.truetype("arial.ttf", 16)
                small_font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()
                small_font = ImageFont.load_default()

            # Nameplate on floor
            nameplate_text = f"{card_info.get('name', 'Professional')}"
            nameplate_x = width * 0.3
            nameplate_y = height * 0.75

            # Background for nameplate
            text_bbox = draw.textbbox((0, 0), nameplate_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            draw.rectangle([nameplate_x - 5, nameplate_y - 5, nameplate_x + text_width + 5, nameplate_y + 25],
                         fill='#FFD700', outline='#000000')

            # Nameplate text
            draw.text((nameplate_x, nameplate_y), nameplate_text, fill='black', font=font)

            # Company info on wall
            company_info = f"{card_info.get('title', 'Professional')}\n{card_info.get('company', 'Company')}\n{card_info.get('email', '<EMAIL>')}"
            wall_x = width * 0.05
            wall_y = height * 0.1

            # Background for company info
            lines = company_info.split('\n')
            max_width = max([draw.textbbox((0, 0), line, font=small_font)[2] for line in lines])
            draw.rectangle([wall_x - 5, wall_y - 5, wall_x + max_width + 5, wall_y + len(lines) * 20 + 5],
                         fill='#FFFFFF', outline='#000000')

            # Company info text
            for i, line in enumerate(lines):
                draw.text((wall_x, wall_y + i * 20), line, fill='black', font=small_font)

        except Exception as e:
            print(f"   Error adding nameplate: {e}")
