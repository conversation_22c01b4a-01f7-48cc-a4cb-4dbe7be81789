// Image data for modal navigation
let imageData = [];
let currentImageIndex = 0;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get image data from script tag
    const imageDataScript = document.getElementById('image-data');
    if (imageDataScript) {
        imageData = JSON.parse(imageDataScript.textContent);
    }

    // Auto focus on AI images when page loads
    const aiSection = document.querySelector('.generated-images');
    if (aiSection) {
        aiSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
});

function downloadImage(filename, downloadName) {
    const link = document.createElement('a');
    link.href = '/' + imageData.find(img => img.filename === filename).path;
    link.download = downloadName;
    link.target = '_blank';  // Open in new tab if download fails
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function openImageModal(index) {
    currentImageIndex = index;
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalInfo = document.getElementById('modalInfo');

    modalImage.src = '/' + imageData[index].path;
    modalInfo.textContent = imageData[index].title;
    modal.style.display = 'block';

    // Disable body scroll
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.style.display = 'none';

    // Enable body scroll
    document.body.style.overflow = 'auto';
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % imageData.length;
    const modalImage = document.getElementById('modalImage');
    const modalInfo = document.getElementById('modalInfo');

    modalImage.src = '/' + imageData[currentImageIndex].path;
    modalInfo.textContent = imageData[currentImageIndex].title;
}

function prevImage() {
    currentImageIndex = (currentImageIndex - 1 + imageData.length) % imageData.length;
    const modalImage = document.getElementById('modalImage');
    const modalInfo = document.getElementById('modalInfo');

    modalImage.src = '/' + imageData[currentImageIndex].path;
    modalInfo.textContent = imageData[currentImageIndex].title;
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    const modal = document.getElementById('imageModal');
    if (modal.style.display === 'block') {
        if (e.key === 'Escape') {
            closeImageModal();
        } else if (e.key === 'ArrowRight') {
            nextImage();
        } else if (e.key === 'ArrowLeft') {
            prevImage();
        }
    }
});

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });
    }
});

function resetSession() {
    if (confirm('Bạn có chắc muốn xóa session hiện tại?')) {
        fetch('/reset_session', {
            method: 'POST'
        })
        .then(res => res.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.href = '/';
            }
        })
        .catch(error => {
            alert('Lỗi reset session: ' + error);
        });
    }
}
