* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.main-content {
    padding: 20px;
}

.result-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.input-images {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.input-images h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.input-item {
    text-align: center;
}

.input-item img {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
}

.input-item h4 {
    color: #666;
    font-size: 1em;
}

.card-info {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-info h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: bold;
    color: #555;
    min-width: 100px;
}

.info-value {
    color: #333;
    flex: 1;
    text-align: right;
}

.generated-images {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    order: -1; /* Move to top */
}

.generated-images h3 {
    color: #333;
    margin-bottom: 30px;
    font-size: 2.2em;
    text-align: center;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.image-item {
    text-align: center;
    position: relative;
}

.image-item img {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: contain;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
    cursor: pointer;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    background: #f8f9fa;
}

.image-item img:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9em;
}

.download-btn {
    margin-top: 15px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.view-btn {
    margin-top: 10px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
}

.view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
}

.modal-image {
    max-width: 95%;
    max-height: 85vh;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    object-fit: contain;
}

.modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-nav:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
}

.modal-prev {
    left: 20px;
}

.modal-next {
    right: 20px;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 2em;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #ff6b6b;
}

.modal-info {
    color: white;
    margin-top: 20px;
    font-size: 1.2em;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.action-buttons {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
}

.btn {
    padding: 15px 30px;
    margin: 0 10px;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .result-section {
        grid-template-columns: 1fr;
    }
    
    .input-grid {
        grid-template-columns: 1fr;
    }
    
    .images-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .main-content {
        padding: 20px;
    }
}

/* Expanded Result Layout */
.result-top-section {
    margin-bottom: 40px;
}

.result-bottom-section {
    margin-top: 40px;
}

/* Expanded Info Card */
.apple-info-card-expanded {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.apple-info-body-expanded {
    padding: 20px 0;
}

.apple-info-grid-expanded {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.apple-info-item-expanded {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #007AFF;
}

.apple-info-label {
    font-weight: 600;
    color: #333;
    font-size: 1.1em;
}

.apple-info-value {
    font-size: 1.2em;
    color: #555;
    word-break: break-all;
}

/* Expanded Gallery */
.apple-gallery-card-expanded {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.apple-images-grid-expanded {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.apple-image-item-expanded {
    background: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.apple-image-display-expanded {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.apple-image-display-expanded img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}


