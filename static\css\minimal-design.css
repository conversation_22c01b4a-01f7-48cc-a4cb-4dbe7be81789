/* 
 * AI Generator - Minimal Design System
 * Clean, functional, camera-focused interface
 */

/* ===== MINIMAL DESIGN TOKENS ===== */
:root {
  /* Clean Color Palette */
  --color-primary: #2563eb;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  color: var(--color-gray-900);
  background: var(--color-gray-50);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== LAYOUT ===== */
.minimal-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* ===== HEADER ===== */
.minimal-header {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--space-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.minimal-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.minimal-logo {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  text-decoration: none;
}

.minimal-status {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.875rem;
  color: var(--color-gray-600);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== CAMERA GRID ===== */
.camera-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin: var(--space-8) 0;
}

.camera-card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: var(--transition);
}

.camera-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.camera-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.camera-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.camera-icon {
  font-size: 1.25rem;
}

.camera-title {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.camera-subtitle {
  font-size: 0.875rem;
  color: var(--color-gray-500);
}

.camera-display {
  position: relative;
  aspect-ratio: 4/3;
  background: var(--color-gray-900);
  overflow: hidden;
}

.camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* ===== CONTROLS ===== */
.controls-section {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--space-6);
  margin: var(--space-8) 0;
}

.controls-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-6);
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.control-label {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.minimal-select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background: var(--color-white);
  font-size: 0.875rem;
  color: var(--color-gray-900);
  transition: var(--transition);
}

.minimal-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.button-group {
  display: flex;
  gap: var(--space-3);
}

.minimal-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.minimal-button-primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.minimal-button-primary:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.minimal-button-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.minimal-button-secondary:hover {
  background: var(--color-gray-200);
}

/* ===== PROGRESS ===== */
.progress-section {
  margin-top: var(--space-6);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--color-gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  margin-top: var(--space-2);
  font-size: 0.875rem;
  color: var(--color-gray-600);
  text-align: center;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .camera-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .controls-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .minimal-button {
    width: 100%;
  }
  
  .minimal-container {
    padding: 0 var(--space-4);
  }
}

/* ===== UTILITIES ===== */
.text-center { text-align: center; }
.hidden { display: none; }
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
